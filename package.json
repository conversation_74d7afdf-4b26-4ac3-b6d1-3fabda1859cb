{"name": "encompass-gohighlevel-integration", "version": "1.0.0", "description": "TypeScript-based integration system for syncing mortgage lead data from Encompass to GoHighLevel CRM", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "dev:watch": "ts-node-dev --respawn --transpile-only src/index.ts", "test:sync": "node test.js", "fetch:modern": "node modern-encompass-fetcher.js", "fetch:legacy": "node consolidated-encompass-online-fetcher.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "docker:build": "docker build -t encompass-ghl-integration .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down", "docker:logs": "docker-compose logs -f encompass-ghl-integration"}, "keywords": ["encompass", "gohighlevel", "mortgage", "crm", "integration", "typescript"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "mongodb": "^6.18.0", "mongoose": "^8.17.0", "node-cron": "^3.0.3", "retry": "^0.13.1", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^20.8.0", "@types/node-cron": "^3.0.8", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.0"}, "engines": {"node": ">=18.0.0"}}