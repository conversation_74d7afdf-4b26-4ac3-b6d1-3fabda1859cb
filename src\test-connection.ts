/**
 * Test MongoDB Connection
 * Simple test to verify the new Mongoose connection approach
 */

import { connectDatabase, disconnectDatabase, isConnected, getDatabaseStats } from './config/database';
import { Logger } from './utils/simple-logger';

const logger = new Logger('ConnectionTest');

async function testConnection() {
  logger.info('🧪 Testing MongoDB connection...');

  try {
    // Test 1: Check initial state
    logger.info(`📊 Initial connection state: ${isConnected() ? 'Connected' : 'Disconnected'}`);

    // Test 2: Attempt connection
    logger.info('🔌 Attempting to connect to MongoDB...');
    
    await connectDatabase({
      connectionString: process.env.MONGODB_CONNECTION_STRING || 'mongodb://localhost:27017',
      databaseName: process.env.MONGODB_DATABASE_NAME || 'encompass_integration_test',
      retryDelayMs: 5000,
      connectionTimeout: 10000, // 10 seconds for quick test
      queryTimeout: 10000,
      maxRetries: 2
    });

    // Test 3: Verify connection
    if (isConnected()) {
      logger.info('✅ Successfully connected to MongoDB!');
      
      const stats = getDatabaseStats();
      logger.info('📊 Database stats:', { stats });

      // Test 4: Simple operation
      logger.info('🔍 Testing basic database operation...');
      
      // You can add a simple test query here if needed
      logger.info('✅ Database operations working!');

    } else {
      logger.error('❌ Connection failed - not connected');
    }

  } catch (error) {
    logger.error('💥 Connection test failed:', { error: error instanceof Error ? error.message : String(error) });
    
    // Provide helpful suggestions
    logger.info('💡 Troubleshooting suggestions:');
    logger.info('   1. Install MongoDB: https://www.mongodb.com/try/download/community');
    logger.info('   2. Start MongoDB service: net start MongoDB (Windows)');
    logger.info('   3. Or use Docker: docker run -d -p 27017:27017 mongo:latest');
    logger.info('   4. Check if port 27017 is available: netstat -an | findstr :27017');
    
  } finally {
    // Test 5: Cleanup
    try {
      if (isConnected()) {
        logger.info('🔌 Disconnecting from MongoDB...');
        await disconnectDatabase();
        logger.info('✅ Disconnected successfully');
      }
    } catch (error) {
      logger.error('💥 Error during disconnect:', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  logger.info('🏁 Connection test completed');
}

// Run the test
if (require.main === module) {
  testConnection()
    .then(() => {
      console.log('\n🎉 Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    });
}

export { testConnection };
