/**
 * Encompass Authentication Service
 * Handles OAuth2 authentication for Encompass API with TypeScript
 */

import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { 
  EncompassAuthConfig, 
  EncompassTokenResponse, 
  EncompassAuthError,
  IntegrationError 
} from '../types';
import { Logger } from '../utils/simple-logger';

export class EncompassAuthService {
  private readonly config: EncompassAuthConfig;
  private readonly logger: Logger;
  private accessToken: string | null = null;
  private tokenExpiry: number | null = null;
  private refreshPromise: Promise<string> | null = null;

  constructor(config: EncompassAuthConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.validateConfig();
  }

  /**
   * Validate the authentication configuration
   */
  private validateConfig(): void {
    const requiredFields: (keyof EncompassAuthConfig)[] = [
      'baseUrl', 'clientId', 'clientSecret', 'username', 'password'
    ];

    for (const field of requiredFields) {
      if (!this.config[field]) {
        throw new Error(`Missing required Encompass configuration: ${field}`);
      }
    }
  }

  /**
   * Get access token for Encompass API
   * Implements token caching and automatic refresh
   */
  public async getAccessToken(): Promise<string> {
    // Check if we have a valid token
    if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
      return this.accessToken;
    }

    // If a refresh is already in progress, wait for it
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    // Start token refresh
    this.refreshPromise = this.refreshAccessToken();
    
    try {
      const token = await this.refreshPromise;
      return token;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * Refresh the access token
   */
  private async refreshAccessToken(): Promise<string> {
    try {
      this.logger.info('Refreshing Encompass access token');

      const tokenUrl = `${this.config.baseUrl}/oauth2/v1/token`;
      const credentials = Buffer.from(`${this.config.clientId}:${this.config.clientSecret}`).toString('base64');
      
      // Prepare the username with instance if provided
      const username = this.config.instance 
        ? `${this.config.username}@encompass:${this.config.instance}`
        : this.config.username;

      const requestData = new URLSearchParams({
        grant_type: 'password',
        username: username,
        password: this.config.password
      });

      const response: AxiosResponse<EncompassTokenResponse> = await axios.post(
        tokenUrl,
        requestData.toString(),
        {
          headers: {
            'Authorization': `Basic ${credentials}`,
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          timeout: 30000 // 30 second timeout
        }
      );

      this.accessToken = response.data.access_token;
      // Set expiry to 50 minutes (tokens typically last 1 hour)
      this.tokenExpiry = Date.now() + (50 * 60 * 1000);

      this.logger.info('Encompass access token refreshed successfully', {
        expiresIn: response.data.expires_in,
        tokenType: response.data.token_type
      });

      return this.accessToken;

    } catch (error) {
      this.logger.error('Failed to refresh Encompass access token', { error });
      
      // Clear cached token on failure
      this.accessToken = null;
      this.tokenExpiry = null;

      if (axios.isAxiosError(error)) {
        const authError = error.response?.data as EncompassAuthError;
        throw this.createIntegrationError(
          'AUTH_FAILED',
          `Authentication failed: ${authError?.error_description || error.message}`,
          'getAccessToken',
          true,
          { statusCode: error.response?.status, authError }
        );
      }

      throw this.createIntegrationError(
        'AUTH_ERROR',
        `Unexpected authentication error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'getAccessToken',
        true
      );
    }
  }

  /**
   * Make authenticated API call to Encompass
   */
  public async makeApiCall<T = unknown>(
    url: string, 
    options: AxiosRequestConfig = {}, 
    description = 'API call'
  ): Promise<T> {
    const token = await this.getAccessToken();
    
    const config: AxiosRequestConfig = {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers
      },
      timeout: options.timeout || 60000 // 60 second default timeout
    };

    try {
      this.logger.debug(`Making Encompass API call: ${description}`, { 
        url, 
        method: config.method || 'GET' 
      });

      const response: AxiosResponse<T> = await axios(url, config);
      
      // Rate limiting - wait 200ms between requests
      await this.delay(200);
      
      this.logger.debug(`Encompass API call successful: ${description}`, {
        statusCode: response.status,
        dataSize: JSON.stringify(response.data).length
      });

      return response.data;

    } catch (error) {
      this.logger.error(`Encompass API call failed: ${description}`, { 
        error, 
        url, 
        method: config.method 
      });

      if (axios.isAxiosError(error)) {
        // Handle token expiration
        if (error.response?.status === 401) {
          this.logger.warn('Token expired, clearing cache and retrying');
          this.accessToken = null;
          this.tokenExpiry = null;
          
          // Retry once with new token
          return this.makeApiCall(url, options, description);
        }

        // Handle rate limiting
        if (error.response?.status === 429) {
          const retryAfter = parseInt(error.response.headers['retry-after'] || '60', 10);
          throw this.createIntegrationError(
            'RATE_LIMITED',
            `Rate limited by Encompass API. Retry after ${retryAfter} seconds`,
            description,
            true,
            { retryAfter: retryAfter * 1000 }
          );
        }

        throw this.createIntegrationError(
          'API_ERROR',
          `Encompass API error: ${error.response?.data?.message || error.message}`,
          description,
          error.response?.status ? error.response.status >= 500 : true,
          { 
            statusCode: error.response?.status,
            responseData: error.response?.data 
          }
        );
      }

      throw this.createIntegrationError(
        'NETWORK_ERROR',
        `Network error during Encompass API call: ${error instanceof Error ? error.message : 'Unknown error'}`,
        description,
        true
      );
    }
  }

  /**
   * Force token refresh
   */
  public async forceRefresh(): Promise<string> {
    this.accessToken = null;
    this.tokenExpiry = null;
    this.refreshPromise = null;
    return this.getAccessToken();
  }

  /**
   * Check if the service is authenticated
   */
  public isAuthenticated(): boolean {
    return this.accessToken !== null && 
           this.tokenExpiry !== null && 
           Date.now() < this.tokenExpiry;
  }

  /**
   * Get token expiry information
   */
  public getTokenInfo(): { hasToken: boolean; expiresAt: number | null; expiresIn: number | null } {
    return {
      hasToken: this.accessToken !== null,
      expiresAt: this.tokenExpiry,
      expiresIn: this.tokenExpiry ? Math.max(0, this.tokenExpiry - Date.now()) : null
    };
  }

  /**
   * Clear cached authentication data
   */
  public clearAuth(): void {
    this.accessToken = null;
    this.tokenExpiry = null;
    this.refreshPromise = null;
    this.logger.info('Encompass authentication cache cleared');
  }

  /**
   * Create a standardized integration error
   */
  private createIntegrationError(
    code: string,
    message: string,
    operation: string,
    retryable: boolean,
    context?: Record<string, unknown>
  ): IntegrationError {
    const error = new Error(message) as IntegrationError;
    error.code = code;
    error.service = 'encompass';
    error.operation = operation;
    error.retryable = retryable;
    error.context = context;
    return error;
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
