/**
 * Complete Integration Example
 * Demonstrates the full MongoDB caching and synchronization system
 */

import { IntegrationManagerService } from '../services/integration-manager';
import { getIntegrationConfig, validateIntegrationConfig } from '../config/integration';
import { SyncStatus } from '../types/mongodb';
import { Logger } from '../utils/simple-logger';

async function runCompleteIntegrationExample() {
  console.log('🚀 Starting Complete Integration Example');
  console.log('=====================================\n');

  // Initialize logger
  const logger = new Logger('integration-example');

  try {
    // Step 1: Load and validate configuration
    console.log('📋 Step 1: Loading configuration...');
    const config = getIntegrationConfig();
    
    const configErrors = validateIntegrationConfig(config);
    if (configErrors.length > 0) {
      console.error('❌ Configuration validation failed:');
      configErrors.forEach(error => console.error(`  - ${error}`));
      return;
    }
    console.log('✅ Configuration loaded and validated\n');

    // Step 2: Initialize Integration Manager
    console.log('🔧 Step 2: Initializing Integration Manager...');
    const integrationManager = new IntegrationManagerService(config, logger);
    await integrationManager.initialize();
    console.log('✅ Integration Manager initialized\n');

    // Step 3: Health Check
    console.log('🏥 Step 3: Performing health check...');
    const healthCheck = await integrationManager.healthCheck();
    console.log(`Health Status: ${healthCheck.status}`);
    console.log(`MongoDB Connected: ${healthCheck.services.mongodb.isConnected}`);
    console.log(`Sync Scheduler Running: ${healthCheck.services.syncScheduler.isRunning}\n`);

    // Step 4: Get total borrower count from Encompass
    console.log('📊 Step 4: Getting total borrower count from Encompass...');
    const totalCount = await integrationManager.getTotalBorrowerCount();
    console.log(`✅ Total borrowers in Encompass: ${totalCount.toLocaleString()}\n`);

    // Step 5: Fetch and cache contacts from Encompass
    console.log('📥 Step 5: Fetching and caching contacts from Encompass...');
    const contacts = await integrationManager.fetchAndCacheContacts(50, 0); // Fetch 50 contacts
    console.log(`✅ Fetched and cached ${contacts.length} contacts\n`);

    // Step 6: Display sample contact data
    if (contacts.length > 0) {
      console.log('👤 Step 6: Sample contact data:');
      const sample = contacts[0];
      console.log(`  Name: ${sample.borrower.name}`);
      console.log(`  Email: ${sample.borrower.email || 'N/A'}`);
      console.log(`  Phone: ${sample.borrower.phones.mobile || sample.borrower.phones.home || 'N/A'}`);
      console.log(`  Address: ${sample.borrower.address?.city || 'N/A'}, ${sample.borrower.address?.state || 'N/A'}`);
      console.log(`  Referral: ${sample.borrower.referralText || 'N/A'}`);
      console.log(`  Source: ${sample.metadata.source}`);
      console.log(`  Version: ${sample.metadata.version}\n`);
    }

    // Step 7: Check MongoDB cache statistics
    console.log('📈 Step 7: MongoDB cache statistics...');
    const dashboardData = await integrationManager.getDashboardData();
    console.log(`  Total contacts in cache: ${dashboardData.mongoStats.totalContacts}`);
    console.log(`  Pending sync: ${dashboardData.mongoStats.pendingSync}`);
    console.log(`  Completed sync: ${dashboardData.mongoStats.completedSync}`);
    console.log(`  Failed sync: ${dashboardData.mongoStats.failedSync}`);
    console.log(`  Error rate: ${dashboardData.mongoStats.errorRate.toFixed(2)}%\n`);

    // Step 8: Search contacts in MongoDB cache
    console.log('🔍 Step 8: Searching contacts in MongoDB cache...');
    const searchResults = await integrationManager.searchContacts(
      { syncStatus: SyncStatus.PENDING },
      { limit: 10, sortBy: 'updatedAt', sortOrder: 'desc' }
    );
    console.log(`✅ Found ${searchResults.data.length} pending contacts (${searchResults.total} total)`);
    
    if (searchResults.data.length > 0) {
      console.log('  Sample pending contacts:');
      searchResults.data.slice(0, 3).forEach((contact, index) => {
        console.log(`    ${index + 1}. ${contact.borrower.name} (${contact.encompassId})`);
      });
    }
    console.log();

    // Step 9: Get contacts ready for GoHighLevel sync
    console.log('🔄 Step 9: Getting contacts ready for GoHighLevel sync...');
    const contactsForSync = await integrationManager.getContactsForGHLSync(10);
    console.log(`✅ Found ${contactsForSync.length} contacts ready for GHL sync\n`);

    // Step 10: Simulate GoHighLevel sync (mark some as synced)
    console.log('🎯 Step 10: Simulating GoHighLevel sync...');
    let syncedCount = 0;
    for (const contact of contactsForSync.slice(0, 3)) { // Sync first 3 contacts
      try {
        // Simulate successful sync
        const ghlContactId = `ghl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await integrationManager.markContactSynced(contact.encompassId, ghlContactId);
        syncedCount++;
        console.log(`  ✅ Synced: ${contact.borrower.name} -> ${ghlContactId}`);
      } catch (error) {
        console.log(`  ❌ Failed to sync: ${contact.borrower.name}`);
      }
    }
    console.log(`✅ Successfully synced ${syncedCount} contacts to GoHighLevel\n`);

    // Step 11: Run manual sync operation
    console.log('⚡ Step 11: Running manual sync operation...');
    const syncResult = await integrationManager.runManualSync();
    console.log(`  Sync ID: ${syncResult.syncId}`);
    console.log(`  Success: ${syncResult.success}`);
    console.log(`  Total processed: ${syncResult.totalProcessed}`);
    console.log(`  New contacts: ${syncResult.newContacts}`);
    console.log(`  Updated contacts: ${syncResult.updatedContacts}`);
    console.log(`  Synced to GHL: ${syncResult.syncedToGHL}`);
    console.log(`  Failed contacts: ${syncResult.failedContacts}`);
    console.log(`  Duration: ${syncResult.duration}ms\n`);

    // Step 12: Updated statistics after sync
    console.log('📊 Step 12: Updated statistics after sync...');
    const updatedDashboard = await integrationManager.getDashboardData();
    console.log(`  Total contacts: ${updatedDashboard.mongoStats.totalContacts}`);
    console.log(`  Pending sync: ${updatedDashboard.mongoStats.pendingSync}`);
    console.log(`  Completed sync: ${updatedDashboard.mongoStats.completedSync}`);
    console.log(`  Last sync: ${updatedDashboard.mongoStats.lastSyncTime || 'Never'}\n`);

    // Step 13: Search for recently synced contacts
    console.log('🔍 Step 13: Searching for recently synced contacts...');
    const syncedContacts = await integrationManager.searchContacts(
      { syncStatus: SyncStatus.COMPLETED },
      { limit: 5, sortBy: 'lastSyncedAt', sortOrder: 'desc' }
    );
    console.log(`✅ Found ${syncedContacts.data.length} recently synced contacts`);
    
    if (syncedContacts.data.length > 0) {
      console.log('  Recently synced:');
      syncedContacts.data.forEach((contact, index) => {
        console.log(`    ${index + 1}. ${contact.borrower.name} -> ${contact.ghlContactId} (${contact.lastSyncedAt})`);
      });
    }
    console.log();

    // Step 14: Cleanup old sync logs
    console.log('🧹 Step 14: Cleaning up old sync logs...');
    const cleanedLogs = await integrationManager.cleanupOldSyncLogs(7); // Keep last 7 days
    console.log(`✅ Cleaned up ${cleanedLogs} old sync logs\n`);

    // Step 15: Final health check
    console.log('🏥 Step 15: Final health check...');
    const finalHealthCheck = await integrationManager.healthCheck();
    console.log(`  Overall Status: ${finalHealthCheck.status}`);
    console.log(`  MongoDB Health: ${finalHealthCheck.services.mongodb.isConnected ? 'Healthy' : 'Unhealthy'}`);
    console.log(`  Sync Scheduler: ${finalHealthCheck.services.syncScheduler.isRunning ? 'Running' : 'Stopped'}`);
    console.log(`  Timestamp: ${finalHealthCheck.timestamp}\n`);

    console.log('🎉 Complete Integration Example finished successfully!');
    console.log('\n📋 Summary:');
    console.log(`  - Fetched ${contacts.length} contacts from Encompass`);
    console.log(`  - Cached contacts in MongoDB with change detection`);
    console.log(`  - Simulated GoHighLevel sync for ${syncedCount} contacts`);
    console.log(`  - Ran automated sync operation`);
    console.log(`  - Demonstrated search and statistics functionality`);
    console.log(`  - System is ready for production use!`);

    // Shutdown gracefully
    await integrationManager.shutdown();
    console.log('\n✅ Integration Manager shutdown completed');

  } catch (error) {
    console.error('\n💥 Integration example failed:', error);
    
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        stack: error.stack
      });
    }
  }
}

// Run the example
if (require.main === module) {
  runCompleteIntegrationExample()
    .then(() => {
      console.log('\n✅ Example completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Example failed:', error.message);
      process.exit(1);
    });
}

export { runCompleteIntegrationExample };
