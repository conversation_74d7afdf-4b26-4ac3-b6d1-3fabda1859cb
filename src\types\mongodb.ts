/**
 * MongoDB Type Definitions for Contact Caching and Synchronization
 */

import { ComprehensiveBorrowerRecord } from './integration';

// MongoDB Contact Document
export interface MongoContactDocument extends ComprehensiveBorrowerRecord {
  _id?: string;
  encompassId: string; // Unique identifier from Encompass
  createdAt: Date;
  updatedAt: Date;
  lastSyncedAt?: Date;
  syncStatus: SyncStatus;
  syncRetryCount: number;
  lastSyncError?: string;
  isDirty: boolean; // Flag indicating if contact needs to be synced to GoHighLevel
  ghlContactId?: string; // GoHighLevel contact ID after successful sync
  dataHash: string; // Hash of the contact data for change detection
}

// Sync Status Enum
export enum SyncStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SKIPPED = 'skipped'
}

// MongoDB Configuration
export interface MongoConfig {
  connectionString: string;
  databaseName: string;
  contactsCollectionName: string;
  syncLogsCollectionName: string;
  maxRetries: number;
  retryDelayMs: number;
  connectionTimeout: number;
  queryTimeout: number;
}

// Sync Log Document
export interface SyncLogDocument {
  _id?: string;
  syncId: string;
  startedAt: Date;
  completedAt?: Date;
  status: SyncStatus;
  totalContacts: number;
  newContacts: number;
  updatedContacts: number;
  syncedToGHL: number;
  failedContacts: number;
  errors: SyncError[];
  duration?: number; // in milliseconds
}

// Sync Error
export interface SyncError {
  encompassId: string;
  contactName: string;
  error: string;
  timestamp: Date;
  retryCount: number;
}

// Query Options
export interface ContactQueryOptions {
  limit?: number;
  skip?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  syncStatus?: SyncStatus;
  isDirty?: boolean;
  lastSyncedBefore?: Date;
  lastSyncedAfter?: Date;
}

// Bulk Operation Result
export interface BulkOperationResult {
  insertedCount: number;
  modifiedCount: number;
  upsertedCount: number;
  deletedCount: number;
  matchedCount: number;
  insertedIds: string[];
  upsertedIds: string[];
}

// Change Detection Result
export interface ChangeDetectionResult {
  newContacts: ComprehensiveBorrowerRecord[];
  updatedContacts: {
    existing: MongoContactDocument;
    updated: ComprehensiveBorrowerRecord;
  }[];
  unchangedContacts: MongoContactDocument[];
  totalProcessed: number;
}

// Sync Statistics
export interface SyncStatistics {
  totalContacts: number;
  pendingSync: number;
  completedSync: number;
  failedSync: number;
  lastSyncTime?: Date;
  averageSyncDuration: number;
  errorRate: number;
}

// Database Indexes
export interface DatabaseIndex {
  name: string;
  fields: Record<string, 1 | -1>;
  options?: {
    unique?: boolean;
    sparse?: boolean;
    background?: boolean;
    expireAfterSeconds?: number;
  };
}

// Contact Search Criteria
export interface ContactSearchCriteria {
  encompassId?: string;
  ghlContactId?: string;
  email?: string;
  phone?: string;
  name?: string;
  syncStatus?: SyncStatus;
  isDirty?: boolean;
  dateRange?: {
    field: 'createdAt' | 'updatedAt' | 'lastSyncedAt';
    from?: Date;
    to?: Date;
  };
}

// Pagination Result
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  totalPages: number;
}

// MongoDB Service Configuration
export interface MongoServiceConfig extends MongoConfig {
  enableChangeDetection: boolean;
  enableAutoSync: boolean;
  syncIntervalMinutes: number;
  batchSize: number;
  maxConcurrentSyncs: number;
  enableMetrics: boolean;
  metricsRetentionDays: number;
}

// Health Check Result
export interface MongoHealthCheck {
  isConnected: boolean;
  responseTime: number;
  lastError?: string;
  collections: {
    contacts: {
      count: number;
      indexes: string[];
    };
    syncLogs: {
      count: number;
      indexes: string[];
    };
  };
  metrics: {
    pendingSyncs: number;
    failedSyncs: number;
    lastSyncTime?: Date;
  };
}

// Types are already exported above with their declarations
