/**
 * Integration Manager Service
 * Orchestrates the complete integration between Encompass, MongoDB, and GoHighLevel
 */

import { EncompassBorrowerService } from './encompass-borrower';
import { MongoContactService } from './mongo-contact';
import { SyncSchedulerService } from './sync-scheduler';
import { MongoServiceConfig } from '../types/mongodb';
import { Logger } from '../utils/simple-logger';

export interface IntegrationManagerConfig {
  encompass: {
    baseUrl: string;
    username: string;
    password: string;
    clientId: string;
    clientSecret: string;
    instance: string;
    batchSize?: number;
    enableMongoCache?: boolean;
  };
  mongodb: MongoServiceConfig;
  sync: {
    cronExpression: string;
    batchSize: number;
    maxConcurrentSyncs: number;
    enableAutoSync: boolean;
    syncTimeoutMs: number;
    retryDelayMs: number;
    maxRetries: number;
  };
  gohighlevel?: {
    apiUrl: string;
    apiKey: string;
    locationId: string;
  };
}

export class IntegrationManagerService {
  private encompassService: EncompassBorrowerService;
  private mongoService: MongoContactService;
  private syncScheduler: SyncSchedulerService;
  private logger: Logger;
  private config: IntegrationManagerConfig;
  private isInitialized: boolean = false;

  constructor(config: IntegrationManagerConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;

    // Initialize services
    this.mongoService = new MongoContactService(config.mongodb, logger);
    this.encompassService = new EncompassBorrowerService(
      {
        ...config.encompass,
        enableMongoCache: true
      },
      logger,
      this.mongoService
    );
    this.syncScheduler = new SyncSchedulerService(
      this.encompassService,
      this.mongoService,
      config.sync,
      logger
    );
  }

  /**
   * Initialize all services
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing Integration Manager...');

      // Initialize MongoDB connection
      await this.mongoService.initialize();
      this.logger.info('MongoDB service initialized');

      // Start sync scheduler if enabled
      if (this.config.sync.enableAutoSync) {
        this.syncScheduler.start();
        this.logger.info('Sync scheduler started');
      }

      this.isInitialized = true;
      this.logger.info('Integration Manager initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize Integration Manager', { error });
      throw error;
    }
  }

  /**
   * Shutdown all services gracefully
   */
  public async shutdown(): Promise<void> {
    try {
      this.logger.info('Shutting down Integration Manager...');

      // Stop sync scheduler
      this.syncScheduler.stop();

      // Close MongoDB connection
      await this.mongoService.close();

      this.isInitialized = false;
      this.logger.info('Integration Manager shutdown completed');

    } catch (error) {
      this.logger.error('Error during shutdown', { error });
    }
  }

  /**
   * Run manual sync
   */
  public async runManualSync(): Promise<any> {
    if (!this.isInitialized) {
      throw new Error('Integration Manager not initialized');
    }

    this.logger.info('Starting manual sync...');
    return await this.syncScheduler.runManualSync();
  }

  /**
   * Get comprehensive dashboard data
   */
  public async getDashboardData(): Promise<{
    syncStatus: any;
    mongoStats: any;
    mongoHealth: any;
    recentSyncHistory: any[];
  }> {
    if (!this.isInitialized) {
      throw new Error('Integration Manager not initialized');
    }

    try {
      const [syncStatus, mongoStats, mongoHealth, recentSyncHistory] = await Promise.all([
        this.syncScheduler.getSyncStatus(),
        this.mongoService.getSyncStatistics(),
        this.mongoService.healthCheck(),
        this.syncScheduler.getSyncHistory(5)
      ]);

      return {
        syncStatus,
        mongoStats,
        mongoHealth,
        recentSyncHistory
      };
    } catch (error) {
      this.logger.error('Failed to get dashboard data', { error });
      throw error;
    }
  }

  /**
   * Search contacts in MongoDB cache
   */
  public async searchContacts(criteria: any, options: any = {}) {
    if (!this.isInitialized) {
      throw new Error('Integration Manager not initialized');
    }

    return await this.mongoService.searchContacts(criteria, options);
  }

  /**
   * Get contacts that need to be synced to GoHighLevel
   */
  public async getContactsForGHLSync(limit: number = 100) {
    if (!this.isInitialized) {
      throw new Error('Integration Manager not initialized');
    }

    return await this.mongoService.getContactsForSync(limit);
  }

  /**
   * Mark contact as synced to GoHighLevel
   */
  public async markContactSynced(encompassId: string, ghlContactId: string) {
    if (!this.isInitialized) {
      throw new Error('Integration Manager not initialized');
    }

    return await this.mongoService.markContactSynced(encompassId, ghlContactId);
  }

  /**
   * Mark contact sync as failed
   */
  public async markContactSyncFailed(encompassId: string, error: string) {
    if (!this.isInitialized) {
      throw new Error('Integration Manager not initialized');
    }

    return await this.mongoService.markContactSyncFailed(encompassId, error);
  }

  /**
   * Fetch fresh data from Encompass and update cache
   */
  public async fetchAndCacheContacts(limit: number = 500, start: number = 0) {
    if (!this.isInitialized) {
      throw new Error('Integration Manager not initialized');
    }

    this.logger.info(`Fetching ${limit} contacts from Encompass starting at ${start}`);
    
    // This will automatically save to MongoDB cache due to enableMongoCache=true
    const contacts = await this.encompassService.fetchComprehensiveBorrowerData(limit, start);
    
    this.logger.info(`Successfully fetched and cached ${contacts.length} contacts`);
    return contacts;
  }

  /**
   * Get total borrower count from Encompass
   */
  public async getTotalBorrowerCount() {
    if (!this.isInitialized) {
      throw new Error('Integration Manager not initialized');
    }

    return await this.encompassService.getTotalBorrowerCount();
  }

  /**
   * Update sync scheduler configuration
   */
  public updateSyncConfig(newConfig: any) {
    this.syncScheduler.updateConfig(newConfig);
    this.logger.info('Sync configuration updated');
  }

  /**
   * Cleanup old sync logs
   */
  public async cleanupOldSyncLogs(retentionDays: number = 30) {
    if (!this.isInitialized) {
      throw new Error('Integration Manager not initialized');
    }

    return await this.syncScheduler.cleanupOldSyncLogs(retentionDays);
  }

  /**
   * Health check for all services
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: {
      integrationManager: { status: string; initialized: boolean };
      mongodb: any;
      syncScheduler: any;
    };
    timestamp: string;
  }> {
    try {
      const mongoHealth = await this.mongoService.healthCheck();
      const syncStatus = this.syncScheduler.getSyncStatus();

      const overallStatus = mongoHealth.isConnected && this.isInitialized ? 'healthy' : 'unhealthy';

      return {
        status: overallStatus,
        services: {
          integrationManager: {
            status: this.isInitialized ? 'running' : 'stopped',
            initialized: this.isInitialized
          },
          mongodb: mongoHealth,
          syncScheduler: syncStatus
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Health check failed', { error });
      return {
        status: 'unhealthy',
        services: {
          integrationManager: {
            status: 'error',
            initialized: this.isInitialized
          },
          mongodb: { isConnected: false, error: error instanceof Error ? error.message : String(error) },
          syncScheduler: { isRunning: false, error: 'Health check failed' }
        },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get service instances (for advanced usage)
   */
  public getServices() {
    return {
      encompass: this.encompassService,
      mongo: this.mongoService,
      scheduler: this.syncScheduler
    };
  }

  /**
   * Check if manager is initialized
   */
  public isManagerInitialized(): boolean {
    return this.isInitialized;
  }
}
