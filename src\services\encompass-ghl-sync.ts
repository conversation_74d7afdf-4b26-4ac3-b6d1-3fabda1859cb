/**
 * Encompass to GoHighLevel Sync Service
 * Handles the complete sync process from Encompass to GoHighLevel
 */

import { EncompassBorrowerService } from './encompass-borrower';
import { GoHighLevelService, GoHighLevelConfig } from './gohighlevel';
import { MongoContactService } from './mongo-contact';
import { Logger } from '../utils/simple-logger';
import { ComprehensiveBorrowerRecord } from '../types/integration';
import { MongoServiceConfig } from '../types/mongodb';

export interface SyncConfig {
  encompass: {
    baseUrl: string;
    username: string;
    password: string;
    clientId: string;
    clientSecret: string;
    instance: string;
  };
  gohighlevel: GoHighLevelConfig;
  mongodb?: MongoServiceConfig;
}

export interface ProcessedContact {
  encompassId: string;
  ghlContactId: string | null;
  ghlOpportunityId: string | null;
  action: 'created' | 'updated';
  opportunityAction: 'created' | 'found' | 'failed';
  name: string;
  email: string | null;
  phone: string | null;
}

export interface SyncSummary {
  processedContacts: ProcessedContact[];
  errors: Array<{ description: string; error: any }>;
  apiCallCount: number;
  elapsedTime: number;
  totalProcessed: number;
  contactsCreated: number;
  contactsUpdated: number;
  opportunitiesCreated: number;
  opportunitiesFound: number;
  failedContacts: number;
}

export class EncompassGHLSyncService {
  private encompassService: EncompassBorrowerService;
  private ghlService: GoHighLevelService;
  private mongoService?: MongoContactService;
  private logger: Logger;
  private startTime: number = 0;
  private processedContacts: ProcessedContact[] = [];
  private errors: Array<{ description: string; error: any }> = [];

  constructor(config: SyncConfig) {
    this.logger = new Logger('EncompassGHLSync');

    // Initialize MongoDB service if configured
    if (config.mongodb) {
      this.mongoService = new MongoContactService(config.mongodb, this.logger);
    }

    this.encompassService = new EncompassBorrowerService(config.encompass, this.logger, this.mongoService);
    this.ghlService = new GoHighLevelService(config.gohighlevel);
  }

  /**
   * Initialize the sync service (mainly for MongoDB connection)
   */
  public async initialize(): Promise<void> {
    if (this.mongoService) {
      await this.mongoService.initialize();
      this.logger.info('MongoDB service initialized for sync');
    }
  }

  /**
   * Main integration function
   */
  public async runIntegration(contactLimit: number = 10, startOffset: number = 0): Promise<SyncSummary> {
    this.logger.info(`🚀 Starting Encompass to GoHighLevel integration for ${contactLimit} contacts starting at ${startOffset}...`);
    this.startTime = Date.now();
    this.processedContacts = [];
    this.errors = [];
    this.ghlService.resetCounters();

    try {
      // Step 1: Get Encompass data
      this.logger.info('📥 Step 1: Fetching data from Encompass...');
      const encompassContacts = await this.encompassService.fetchComprehensiveBorrowerData(contactLimit, startOffset);
      this.logger.info(`✅ Retrieved ${encompassContacts.length} contacts from Encompass`);

      if (encompassContacts.length === 0) {
        this.logger.warn('No contacts found in Encompass');
        return this.generateSummary();
      }

      // Step 2: Process each contact
      this.logger.info('\n🔄 Step 2: Processing and pushing to GoHighLevel...');

      for (let i = 0; i < encompassContacts.length; i++) {
        const encompassContact = encompassContacts[i];
        this.logger.info(`\n📄 Processing contact ${i + 1}/${encompassContacts.length}: ${encompassContact.borrower.id}`);

        await this.processContact(encompassContact);
      }

      // Step 3: Summary and results
      this.logger.info('\n📊 Step 3: Integration Summary...');
      const summary = this.generateSummary();
      this.logSummary(summary);

      return summary;

    } catch (error: any) {
      this.logger.error('❌ Integration failed:', { error: error.message });
      this.errors.push({
        description: 'Integration process',
        error: error.message
      });
      return this.generateSummary();
    }
  }

  /**
   * Process a single contact
   */
  private async processContact(encompassContact: ComprehensiveBorrowerRecord): Promise<void> {
    try {
      const borrower = encompassContact.borrower;
      
      this.logger.info(`   ✅ Encompass: ${borrower.firstName || ''} ${borrower.lastName || ''}`);
      this.logger.info(`   📧 Email: ${borrower.email || 'N/A'}`);
      this.logger.info(`   📞 Phone: ${borrower.phones?.home || borrower.phones?.work || borrower.phones?.mobile || 'N/A'}`);

      // Transform to GoHighLevel format
      const ghlContactData = this.ghlService.transformToGHLFormat(encompassContact);

      // Push to GoHighLevel
      const ghlResult = await this.ghlService.createOrUpdateGHLContact(ghlContactData);

      if (ghlResult) {
        this.logger.info(`   ✅ GoHighLevel: Contact ${ghlResult.action} successfully`);
        this.logger.info(`   🆔 GHL Contact ID: ${ghlResult.contactId || 'N/A'}`);

        // Create opportunity if we have pipeline configuration
        let opportunityResult = null;
        if (ghlResult.contactId) {
          this.logger.info(`   🎯 Creating opportunity for contact...`);
          opportunityResult = await this.ghlService.createOpportunity(ghlResult.contactId, ghlContactData);

          if (opportunityResult && opportunityResult.success) {
            this.logger.info(`   ✅ Opportunity ${opportunityResult.isNew ? 'created' : 'found'}: ${opportunityResult.opportunityId}`);
          } else {
            this.logger.info(`   ⚠️ Opportunity creation skipped or failed`);
          }
        } else {
          this.logger.info(`   ℹ️ Opportunity creation skipped (missing contact ID)`);
        }

        // Save to MongoDB if configured
        if (this.mongoService && ghlResult.contactId) {
          try {
            await this.mongoService.saveContact(encompassContact, borrower.id);
            await this.mongoService.markContactSynced(borrower.id, ghlResult.contactId);
            this.logger.debug(`   💾 Saved to MongoDB: ${borrower.id}`);
          } catch (mongoError: any) {
            this.logger.warn(`   ⚠️ Failed to save to MongoDB: ${mongoError.message}`);
          }
        }

        this.processedContacts.push({
          encompassId: borrower.id,
          ghlContactId: ghlResult.contactId,
          ghlOpportunityId: opportunityResult?.opportunityId || null,
          action: ghlResult.action,
          opportunityAction: opportunityResult?.isNew ? 'created' : (opportunityResult?.success ? 'found' : 'failed'),
          name: `${borrower.firstName || ''} ${borrower.lastName || ''}`.trim(),
          email: borrower.email || null,
          phone: borrower.phones?.home || borrower.phones?.work || borrower.phones?.mobile || null
        });
      } else {
        this.logger.error(`   ❌ Failed to create/update in GoHighLevel`);
        this.processedContacts.push({
          encompassId: borrower.id,
          ghlContactId: null,
          ghlOpportunityId: null,
          action: 'created', // Default value
          opportunityAction: 'failed',
          name: `${borrower.firstName || ''} ${borrower.lastName || ''}`.trim(),
          email: borrower.email || null,
          phone: borrower.phones?.home || borrower.phones?.work || borrower.phones?.mobile || null
        });
      }

    } catch (error: any) {
      this.logger.error(`   ❌ Error processing contact: ${error.message}`);
      this.errors.push({
        description: `Processing contact ${encompassContact.borrower.id}`,
        error: error.message
      });
    }
  }

  /**
   * Generate summary of sync operation
   */
  private generateSummary(): SyncSummary {
    const ghlErrors = this.ghlService.getErrors();
    const allErrors = [...this.errors, ...ghlErrors];

    const contactsCreated = this.processedContacts.filter(c => c.action === 'created' && c.ghlContactId).length;
    const contactsUpdated = this.processedContacts.filter(c => c.action === 'updated' && c.ghlContactId).length;
    const opportunitiesCreated = this.processedContacts.filter(c => c.opportunityAction === 'created').length;
    const opportunitiesFound = this.processedContacts.filter(c => c.opportunityAction === 'found').length;
    const failedContacts = this.processedContacts.filter(c => !c.ghlContactId).length;

    return {
      processedContacts: this.processedContacts,
      errors: allErrors,
      apiCallCount: this.ghlService.getApiCallCount(),
      elapsedTime: (Date.now() - this.startTime) / 1000,
      totalProcessed: this.processedContacts.length,
      contactsCreated,
      contactsUpdated,
      opportunitiesCreated,
      opportunitiesFound,
      failedContacts
    };
  }

  /**
   * Log summary to console
   */
  private logSummary(summary: SyncSummary): void {
    this.logger.info('📊 === SYNC SUMMARY ===');
    this.logger.info(`⏱️  Total time: ${summary.elapsedTime.toFixed(2)} seconds`);
    this.logger.info(`📞 API calls made: ${summary.apiCallCount}`);
    this.logger.info(`👥 Total contacts processed: ${summary.totalProcessed}`);
    this.logger.info(`✅ Contacts created: ${summary.contactsCreated}`);
    this.logger.info(`🔄 Contacts updated: ${summary.contactsUpdated}`);
    this.logger.info(`🎯 Opportunities created: ${summary.opportunitiesCreated}`);
    this.logger.info(`🔍 Opportunities found: ${summary.opportunitiesFound}`);
    this.logger.info(`❌ Failed contacts: ${summary.failedContacts}`);
    this.logger.info(`🚨 Total errors: ${summary.errors.length}`);

    if (summary.errors.length > 0) {
      this.logger.warn('\n🚨 Errors encountered:');
      summary.errors.forEach((error, index) => {
        this.logger.warn(`  ${index + 1}. ${error.description}: ${JSON.stringify(error.error)}`);
      });
    }

    if (summary.processedContacts.length > 0) {
      this.logger.info('\n👥 Processed contacts:');
      summary.processedContacts.forEach((contact, index) => {
        const status = contact.ghlContactId ? '✅' : '❌';
        this.logger.info(`  ${index + 1}. ${status} ${contact.name} (${contact.action}) - GHL ID: ${contact.ghlContactId || 'Failed'}`);
      });
    }
  }
}
