/**
 * MongoDB Contact Service
 * Handles caching and synchronization of Encompass borrower contacts
 */

import { MongoClient, Db, Collection } from 'mongodb';
import * as crypto from 'crypto';
import { 
  MongoContactDocument, 
  SyncStatus, 
  MongoServiceConfig,
  SyncLogDocument,
  ChangeDetectionResult,
  ContactQueryOptions,
  SyncStatistics,
  ContactSearchCriteria,
  PaginatedResult,
  MongoHealthCheck,
  DatabaseIndex
} from '../types/mongodb';
import { ComprehensiveBorrowerRecord } from '../types/integration';
import { Logger } from '../utils/simple-logger';

export class MongoContactService {
  private client: MongoClient;
  private db!: Db;
  private contactsCollection!: Collection<MongoContactDocument>;
  private syncLogsCollection!: Collection<SyncLogDocument>;
  private logger: Logger;
  private config: MongoServiceConfig;
  private isConnected: boolean = false;

  constructor(config: MongoServiceConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.client = new MongoClient(config.connectionString, {
      connectTimeoutMS: config.connectionTimeout,
      socketTimeoutMS: config.queryTimeout,
      maxPoolSize: 10,
      retryWrites: true,
      retryReads: true
    });
  }

  /**
   * Initialize MongoDB connection and setup collections
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing MongoDB connection...');
      
      await this.client.connect();
      this.db = this.client.db(this.config.databaseName);
      
      this.contactsCollection = this.db.collection<MongoContactDocument>(this.config.contactsCollectionName);
      this.syncLogsCollection = this.db.collection<SyncLogDocument>(this.config.syncLogsCollectionName);
      
      await this.createIndexes();
      this.isConnected = true;
      
      this.logger.info('MongoDB connection initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize MongoDB connection', { error });
      throw new Error(`MongoDB initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create database indexes for optimal performance
   */
  private async createIndexes(): Promise<void> {
    const indexes: DatabaseIndex[] = [
      {
        name: 'encompassId_unique',
        fields: { encompassId: 1 },
        options: { unique: true, background: true }
      },
      {
        name: 'syncStatus_isDirty',
        fields: { syncStatus: 1, isDirty: 1 },
        options: { background: true }
      },
      {
        name: 'lastSyncedAt',
        fields: { lastSyncedAt: 1 },
        options: { background: true }
      },
      {
        name: 'updatedAt',
        fields: { updatedAt: -1 },
        options: { background: true }
      },
      {
        name: 'ghlContactId',
        fields: { ghlContactId: 1 },
        options: { sparse: true, background: true }
      },
      {
        name: 'borrower_email',
        fields: { 'borrower.email': 1 },
        options: { sparse: true, background: true }
      }
    ];

    for (const index of indexes) {
      try {
        await this.contactsCollection.createIndex(index.fields, {
          name: index.name,
          ...index.options
        });
        this.logger.debug(`Created index: ${index.name}`);
      } catch (error) {
        this.logger.warn(`Failed to create index ${index.name}`, { error });
      }
    }

    // Sync logs indexes
    await this.syncLogsCollection.createIndex({ startedAt: -1 }, { background: true });
    await this.syncLogsCollection.createIndex({ syncId: 1 }, { unique: true, background: true });
  }

  /**
   * Generate hash for contact data to detect changes
   */
  private generateDataHash(contact: ComprehensiveBorrowerRecord): string {
    const dataString = JSON.stringify({
      borrower: contact.borrower,
      loan: contact.loan,
      realtor: contact.realtor
    });
    return crypto.createHash('sha256').update(dataString).digest('hex');
  }

  /**
   * Save or update a single contact
   */
  public async saveContact(
    contact: ComprehensiveBorrowerRecord, 
    encompassId: string
  ): Promise<MongoContactDocument> {
    try {
      const now = new Date();
      const dataHash = this.generateDataHash(contact);
      
      const existingContact = await this.contactsCollection.findOne({ encompassId });
      
      if (existingContact) {
        // Check if data has changed
        if (existingContact.dataHash !== dataHash) {
          const updateDoc: Partial<MongoContactDocument> = {
            ...contact,
            updatedAt: now,
            dataHash,
            isDirty: true,
            syncStatus: SyncStatus.PENDING,
            syncRetryCount: 0,
            lastSyncError: undefined
          };
          
          await this.contactsCollection.updateOne(
            { encompassId },
            { $set: updateDoc }
          );
          
          this.logger.debug(`Updated contact: ${encompassId}`);
          return { ...existingContact, ...updateDoc } as MongoContactDocument;
        } else {
          this.logger.debug(`No changes detected for contact: ${encompassId}`);
          return existingContact;
        }
      } else {
        // Create new contact
        const newContact: MongoContactDocument = {
          ...contact,
          encompassId,
          createdAt: now,
          updatedAt: now,
          syncStatus: SyncStatus.PENDING,
          syncRetryCount: 0,
          isDirty: true,
          dataHash
        };
        
        await this.contactsCollection.insertOne(newContact);
        this.logger.debug(`Created new contact: ${encompassId}`);
        return newContact;
      }
    } catch (error) {
      this.logger.error(`Failed to save contact ${encompassId}`, { error });
      throw error;
    }
  }

  /**
   * Bulk save contacts with change detection
   */
  public async saveContacts(
    contacts: { contact: ComprehensiveBorrowerRecord; encompassId: string }[]
  ): Promise<ChangeDetectionResult> {
    try {
      this.logger.info(`Processing ${contacts.length} contacts for bulk save`);
      
      const result: ChangeDetectionResult = {
        newContacts: [],
        updatedContacts: [],
        unchangedContacts: [],
        totalProcessed: contacts.length
      };

      const bulkOps = [];
      const now = new Date();

      for (const { contact, encompassId } of contacts) {
        const dataHash = this.generateDataHash(contact);
        const existingContact = await this.contactsCollection.findOne({ encompassId });

        if (existingContact) {
          if (existingContact.dataHash !== dataHash) {
            // Contact has changed
            result.updatedContacts.push({
              existing: existingContact,
              updated: contact
            });

            bulkOps.push({
              updateOne: {
                filter: { encompassId },
                update: {
                  $set: {
                    ...contact,
                    updatedAt: now,
                    dataHash,
                    isDirty: true,
                    syncStatus: SyncStatus.PENDING,
                    syncRetryCount: 0,
                    lastSyncError: undefined
                  }
                }
              }
            });
          } else {
            // No changes
            result.unchangedContacts.push(existingContact);
          }
        } else {
          // New contact
          result.newContacts.push(contact);
          
          bulkOps.push({
            insertOne: {
              document: {
                ...contact,
                encompassId,
                createdAt: now,
                updatedAt: now,
                syncStatus: SyncStatus.PENDING,
                syncRetryCount: 0,
                isDirty: true,
                dataHash
              } as MongoContactDocument
            }
          });
        }
      }

      if (bulkOps.length > 0) {
        await this.contactsCollection.bulkWrite(bulkOps);
        this.logger.info(`Bulk operation completed: ${bulkOps.length} operations`);
      }

      this.logger.info('Bulk save completed', {
        total: result.totalProcessed,
        new: result.newContacts.length,
        updated: result.updatedContacts.length,
        unchanged: result.unchangedContacts.length
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to bulk save contacts', { error });
      throw error;
    }
  }

  /**
   * Get contacts that need to be synced to GoHighLevel
   */
  public async getContactsForSync(limit: number = 100): Promise<MongoContactDocument[]> {
    try {
      const contacts = await this.contactsCollection
        .find({
          isDirty: true,
          syncStatus: { $in: [SyncStatus.PENDING, SyncStatus.FAILED] },
          syncRetryCount: { $lt: this.config.maxRetries }
        })
        .sort({ updatedAt: 1 })
        .limit(limit)
        .toArray();

      this.logger.debug(`Found ${contacts.length} contacts for sync`);
      return contacts;
    } catch (error) {
      this.logger.error('Failed to get contacts for sync', { error });
      throw error;
    }
  }

  /**
   * Mark contact as synced to GoHighLevel
   */
  public async markContactSynced(
    encompassId: string, 
    ghlContactId: string
  ): Promise<void> {
    try {
      await this.contactsCollection.updateOne(
        { encompassId },
        {
          $set: {
            syncStatus: SyncStatus.COMPLETED,
            lastSyncedAt: new Date(),
            ghlContactId,
            isDirty: false,
            syncRetryCount: 0,
            lastSyncError: undefined
          }
        }
      );
      
      this.logger.debug(`Marked contact as synced: ${encompassId} -> ${ghlContactId}`);
    } catch (error) {
      this.logger.error(`Failed to mark contact as synced: ${encompassId}`, { error });
      throw error;
    }
  }

  /**
   * Mark contact sync as failed
   */
  public async markContactSyncFailed(
    encompassId: string, 
    error: string
  ): Promise<void> {
    try {
      await this.contactsCollection.updateOne(
        { encompassId },
        {
          $set: {
            syncStatus: SyncStatus.FAILED,
            lastSyncError: error
          },
          $inc: {
            syncRetryCount: 1
          }
        }
      );
      
      this.logger.debug(`Marked contact sync as failed: ${encompassId}`);
    } catch (error) {
      this.logger.error(`Failed to mark contact sync as failed: ${encompassId}`, { error });
      throw error;
    }
  }

  /**
   * Close MongoDB connection
   */
  public async close(): Promise<void> {
    try {
      await this.client.close();
      this.isConnected = false;
      this.logger.info('MongoDB connection closed');
    } catch (error) {
      this.logger.error('Error closing MongoDB connection', { error });
    }
  }

  /**
   * Search contacts with criteria
   */
  public async searchContacts(
    criteria: ContactSearchCriteria,
    options: ContactQueryOptions = {}
  ): Promise<PaginatedResult<MongoContactDocument>> {
    try {
      const filter: any = {};

      if (criteria.encompassId) filter.encompassId = criteria.encompassId;
      if (criteria.ghlContactId) filter.ghlContactId = criteria.ghlContactId;
      if (criteria.syncStatus) filter.syncStatus = criteria.syncStatus;
      if (criteria.isDirty !== undefined) filter.isDirty = criteria.isDirty;
      if (criteria.email) filter['borrower.email'] = new RegExp(criteria.email, 'i');
      if (criteria.name) {
        filter.$or = [
          { 'borrower.firstName': new RegExp(criteria.name, 'i') },
          { 'borrower.lastName': new RegExp(criteria.name, 'i') },
          { 'borrower.name': new RegExp(criteria.name, 'i') }
        ];
      }

      if (criteria.dateRange) {
        const dateFilter: any = {};
        if (criteria.dateRange.from) dateFilter.$gte = criteria.dateRange.from;
        if (criteria.dateRange.to) dateFilter.$lte = criteria.dateRange.to;
        filter[criteria.dateRange.field] = dateFilter;
      }

      const limit = options.limit || 50;
      const skip = options.skip || 0;
      const sortField = options.sortBy || 'updatedAt';
      const sortOrder = options.sortOrder === 'asc' ? 1 : -1;

      const [data, total] = await Promise.all([
        this.contactsCollection
          .find(filter)
          .sort({ [sortField]: sortOrder })
          .skip(skip)
          .limit(limit)
          .toArray(),
        this.contactsCollection.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limit);
      const currentPage = Math.floor(skip / limit) + 1;

      return {
        data,
        total,
        page: currentPage,
        limit,
        hasMore: skip + limit < total,
        totalPages
      };
    } catch (error) {
      this.logger.error('Failed to search contacts', { error, criteria });
      throw error;
    }
  }

  /**
   * Get sync statistics
   */
  public async getSyncStatistics(): Promise<SyncStatistics> {
    try {
      const [
        totalContacts,
        pendingSync,
        completedSync,
        failedSync,
        lastSyncLog
      ] = await Promise.all([
        this.contactsCollection.countDocuments(),
        this.contactsCollection.countDocuments({ syncStatus: SyncStatus.PENDING }),
        this.contactsCollection.countDocuments({ syncStatus: SyncStatus.COMPLETED }),
        this.contactsCollection.countDocuments({ syncStatus: SyncStatus.FAILED }),
        this.syncLogsCollection.findOne({}, { sort: { startedAt: -1 } })
      ]);

      const recentLogs = await this.syncLogsCollection
        .find({ completedAt: { $exists: true } })
        .sort({ startedAt: -1 })
        .limit(10)
        .toArray();

      const averageSyncDuration = recentLogs.length > 0
        ? recentLogs.reduce((sum, log) => sum + (log.duration || 0), 0) / recentLogs.length
        : 0;

      const errorRate = totalContacts > 0 ? (failedSync / totalContacts) * 100 : 0;

      return {
        totalContacts,
        pendingSync,
        completedSync,
        failedSync,
        lastSyncTime: lastSyncLog?.startedAt,
        averageSyncDuration,
        errorRate
      };
    } catch (error) {
      this.logger.error('Failed to get sync statistics', { error });
      throw error;
    }
  }

  /**
   * Create sync log entry
   */
  public async createSyncLog(syncId: string): Promise<SyncLogDocument> {
    try {
      const syncLog: SyncLogDocument = {
        syncId,
        startedAt: new Date(),
        status: SyncStatus.IN_PROGRESS,
        totalContacts: 0,
        newContacts: 0,
        updatedContacts: 0,
        syncedToGHL: 0,
        failedContacts: 0,
        errors: []
      };

      await this.syncLogsCollection.insertOne(syncLog);
      this.logger.debug(`Created sync log: ${syncId}`);
      return syncLog;
    } catch (error) {
      this.logger.error(`Failed to create sync log: ${syncId}`, { error });
      throw error;
    }
  }

  /**
   * Update sync log
   */
  public async updateSyncLog(
    syncId: string,
    updates: Partial<SyncLogDocument>
  ): Promise<void> {
    try {
      const updateDoc = {
        ...updates,
        ...(updates.status === SyncStatus.COMPLETED && { completedAt: new Date() })
      };

      if (updateDoc.completedAt && updateDoc.startedAt) {
        updateDoc.duration = updateDoc.completedAt.getTime() - updateDoc.startedAt.getTime();
      }

      await this.syncLogsCollection.updateOne(
        { syncId },
        { $set: updateDoc }
      );

      this.logger.debug(`Updated sync log: ${syncId}`);
    } catch (error) {
      this.logger.error(`Failed to update sync log: ${syncId}`, { error });
      throw error;
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<MongoHealthCheck> {
    try {
      const startTime = Date.now();

      // Test connection
      await this.db.admin().ping();
      const responseTime = Date.now() - startTime;

      const [contactsCount, syncLogsCount, stats] = await Promise.all([
        this.contactsCollection.countDocuments(),
        this.syncLogsCollection.countDocuments(),
        this.getSyncStatistics()
      ]);

      const contactsIndexes = await this.contactsCollection.listIndexes().toArray();
      const syncLogsIndexes = await this.syncLogsCollection.listIndexes().toArray();

      return {
        isConnected: true,
        responseTime,
        collections: {
          contacts: {
            count: contactsCount,
            indexes: contactsIndexes.map(idx => idx.name)
          },
          syncLogs: {
            count: syncLogsCount,
            indexes: syncLogsIndexes.map(idx => idx.name)
          }
        },
        metrics: {
          pendingSyncs: stats.pendingSync,
          failedSyncs: stats.failedSync,
          lastSyncTime: stats.lastSyncTime
        }
      };
    } catch (error) {
      this.logger.error('Health check failed', { error });
      return {
        isConnected: false,
        responseTime: -1,
        lastError: error instanceof Error ? error.message : String(error),
        collections: {
          contacts: { count: 0, indexes: [] },
          syncLogs: { count: 0, indexes: [] }
        },
        metrics: {
          pendingSyncs: 0,
          failedSyncs: 0
        }
      };
    }
  }

  /**
   * Get contacts collection (for advanced operations)
   */
  public getContactsCollection(): Collection<MongoContactDocument> {
    return this.contactsCollection;
  }

  /**
   * Get sync logs collection (for advanced operations)
   */
  public getSyncLogsCollection(): Collection<SyncLogDocument> {
    return this.syncLogsCollection;
  }

  /**
   * Check if service is connected
   */
  public isServiceConnected(): boolean {
    return this.isConnected;
  }
}
