/**
 * Encompass Borrower Service
 * Handles all borrower-related operations with Encompass API in TypeScript
 */

import {
  EncompassAuthConfig,
  EncompassBorrowerContact,
  EncompassBatchContact,
  EncompassLoanData,
  EncompassLoanAssociate,
  EncompassBusinessContact,
  ComprehensiveBorrowerRecord,
  ENCOMPASS_FIELD_IDS
} from '../types';
import { EncompassAuthService } from './encompass-auth';
import { MongoContactService } from './mongo-contact';
import { Logger } from '../utils/simple-logger';

export interface BorrowerServiceConfig extends EncompassAuthConfig {
  batchSize?: number;
  maxRetries?: number;
  enableMongoCache?: boolean;
}

export class EncompassBorrowerService {
  private readonly authService: EncompassAuthService;
  private readonly baseUrl: string;
  private readonly logger: Logger;
  private readonly batchSize: number;
  // private readonly maxRetries: number; // Currently unused but kept for future use
  private readonly enableMongoCache: boolean;
  private mongoService?: MongoContactService;

  constructor(config: BorrowerServiceConfig, logger: Logger, mongoService?: MongoContactService) {
    this.authService = new EncompassAuthService(config, logger);
    this.baseUrl = config.baseUrl;
    this.logger = logger;
    this.batchSize = config.batchSize || 100;
    // this.maxRetries = config.maxRetries || 3;
    this.enableMongoCache = config.enableMongoCache || false;
    this.mongoService = mongoService;
  }

  /**
   * Get total count of borrower contacts using the working borrowerContacts endpoint
   */
  public async getTotalBorrowerCount(): Promise<number> {
    try {
      this.logger.info('Getting total borrower count using borrowerContacts endpoint');

      // Use the working endpoint to get actual count
      const response = await this.authService.makeApiCall<EncompassBorrowerContact[]>(
        `${this.baseUrl}/encompass/v1/borrowerContacts?limit=1000&start=0`,
        { method: 'GET' },
        'Getting borrower count'
      );

      const estimatedCount = response.length;
      this.logger.info(`Estimated borrower contacts: ${estimatedCount.toLocaleString()}`);

      return estimatedCount;

    } catch (error) {
      this.logger.error('Error getting borrower count', { error });

      if (error instanceof Error && 'retryable' in error) {
        throw error;
      }

      // If the API fails, return 0 for now
      this.logger.warn('Could not determine borrower count, returning 0');
      return 0;
    }
  }

  /**
   * Get borrower contacts using the working borrowerContacts endpoint
   */
  public async getBorrowerContacts(
    limit: number = 500,
    start: number = 0
  ): Promise<{
    contacts: EncompassBatchContact[];
    totalRecords: number;
    currentStart: number;
    currentLimit: number;
    hasMore: boolean;
  }> {
    try {
      const actualLimit = Math.min(limit, 1000); // Use reasonable batch size
      const url = `${this.baseUrl}/encompass/v1/borrowerContacts?limit=${actualLimit}&start=${start}`;

      this.logger.debug(`Getting ${actualLimit} borrower contacts starting from ${start}`);

      // Use the working borrowerContacts endpoint - no fields specification needed
      const response = await this.authService.makeApiCall<EncompassBatchContact[]>(
        url,
        { method: 'GET' },
        `Getting ${actualLimit} borrower contacts starting from ${start}`
      );

      const result = {
        contacts: response || [],
        totalRecords: response.length, // We can only estimate based on what we got
        currentStart: start,
        currentLimit: actualLimit,
        hasMore: response.length === actualLimit // If we got full batch, there might be more
      };

      this.logger.debug(`Retrieved ${result.contacts.length} contacts`, {
        totalRecords: result.totalRecords,
        hasMore: result.hasMore
      });

      return result;

    } catch (error) {
      this.logger.error('Error getting borrower contacts', { error, limit, start });
      throw error;
    }
  }

  /**
   * Fetch comprehensive borrower data using simplified batch approach
   */
  public async fetchComprehensiveBorrowerData(
    limit: number = 500,
    start: number = 0
  ): Promise<ComprehensiveBorrowerRecord[]> {
    try {
      this.logger.info(`Fetching comprehensive borrower data (limit: ${limit}, start: ${start})`);

      // Get borrower contacts using the working endpoint
      const result = await this.getBorrowerContacts(limit, start);

      if (result.contacts.length === 0) {
        this.logger.info('No borrower contacts found');
        return [];
      }

      this.logger.info(`Found ${result.contacts.length} borrower contacts, processing...`);

      // Process contacts to create comprehensive records (no additional API calls needed)
      const comprehensiveRecords: ComprehensiveBorrowerRecord[] = [];

      for (const contact of result.contacts) {
        try {
          const record = this.createComprehensiveRecordFromContact(contact);
          comprehensiveRecords.push(record);
        } catch (error) {
          this.logger.error(`Error processing contact ${contact.contactId}`, { error, contactId: contact.contactId });
          // Continue with other contacts
        }
      }

      this.logger.info(`Successfully processed ${comprehensiveRecords.length}/${result.contacts.length} contacts`);

      // Save to MongoDB if caching is enabled
      if (this.enableMongoCache && this.mongoService) {
        try {
          this.logger.info('Saving contacts to MongoDB cache...');
          const changeResult = await this.mongoService.saveContacts(
            comprehensiveRecords.map(record => ({
              contact: record,
              encompassId: record.borrower.id
            }))
          );

          this.logger.info('MongoDB cache updated', {
            new: changeResult.newContacts.length,
            updated: changeResult.updatedContacts.length,
            unchanged: changeResult.unchangedContacts.length
          });
        } catch (error) {
          this.logger.error('Failed to save contacts to MongoDB cache', { error });
          // Don't throw error - continue with the operation
        }
      }

      return comprehensiveRecords;

    } catch (error) {
      this.logger.error('Error fetching comprehensive borrower data', { error, limit, start });
      throw new Error(
        `Failed to fetch comprehensive borrower data: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get all borrower contacts with pagination
   */
  public async getAllBorrowerContacts(maxContacts?: number): Promise<EncompassBatchContact[]> {
    try {
      this.logger.info('Getting all borrower contacts with pagination');

      const allContacts: EncompassBatchContact[] = [];
      let start = 1;
      let hasMore = true;
      const limit = this.batchSize;

      while (hasMore && (!maxContacts || allContacts.length < maxContacts)) {
        const response = await this.getBorrowerContacts(limit, start);
        
        allContacts.push(...response.contacts);
        hasMore = response.hasMore;
        start += limit;

        this.logger.debug(`Fetched batch: ${response.contacts.length} contacts, total so far: ${allContacts.length}`);

        // Respect max contacts limit
        if (maxContacts && allContacts.length >= maxContacts) {
          allContacts.splice(maxContacts);
          break;
        }

        // Rate limiting between batches
        if (hasMore) {
          await this.delay(1000); // 1 second between batches
        }
      }

      this.logger.info(`Retrieved ${allContacts.length} total borrower contacts`);
      return allContacts;

    } catch (error) {
      this.logger.error('Error getting all borrower contacts', { error });
      throw error;
    }
  }

  /**
   * Get detailed borrower contact information for missing fields like dateOfBirth
   */
  public async getBorrowerContactDetails(contactId: string): Promise<EncompassBorrowerContact> {
    try {
      this.logger.debug(`Getting contact details for ${contactId}`);

      const response = await this.authService.makeApiCall<EncompassBorrowerContact>(
        `${this.baseUrl}/encompass/v1/borrowerContacts/${contactId}`,
        { method: 'GET' },
        `Getting Encompass contact details for ${contactId}`
      );

      return response;

    } catch (error) {
      this.logger.error(`Error getting borrower contact details for ${contactId}`, { error });
      throw error;
    }
  }

  /**
   * Enhance batch contact with individual contact details if needed
   */
  public async enhanceBatchContactWithDetails(
    batchContact: EncompassBatchContact,
    fetchMissingFields: boolean = true
  ): Promise<ComprehensiveBorrowerRecord> {
    let enhancedContact = batchContact;

    // If dateOfBirth is missing and we want to fetch missing fields
    if (fetchMissingFields && !batchContact.dateOfBirth) {
      try {
        this.logger.debug(`Fetching individual contact details for missing dateOfBirth: ${batchContact.contactId}`);
        const individualContact = await this.getBorrowerContactDetails(batchContact.contactId);

        // Enhance the batch contact with missing data
        enhancedContact = {
          ...batchContact,
          dateOfBirth: individualContact.birthdate || batchContact.dateOfBirth
        };

        this.logger.debug(`Enhanced contact ${batchContact.contactId} with dateOfBirth: ${individualContact.birthdate}`);
      } catch (error) {
        this.logger.warn(`Failed to fetch individual contact details for ${batchContact.contactId}`, { error });
        // Continue with original batch contact data
      }
    }

    return this.createComprehensiveRecordFromContact(enhancedContact);
  }

  /**
   * Get loan data for a borrower (when available)
   */
  public async getLoanData(loanId: string): Promise<EncompassLoanData | null> {
    try {
      this.logger.debug(`Getting loan data for ${loanId}`);

      const response = await this.authService.makeApiCall<EncompassLoanData>(
        `${this.baseUrl}/encompass/v1/loans/${loanId}`,
        { method: 'GET' },
        `Getting loan data for ${loanId}`
      );

      return response;

    } catch (error) {
      this.logger.warn(`Could not get loan data for ${loanId}`, { error });
      // Return null if loan data is not accessible
      return null;
    }
  }

  /**
   * Get loan associates for a loan (when available)
   */
  public async getLoanAssociates(loanId: string): Promise<EncompassLoanAssociate[] | null> {
    try {
      this.logger.debug(`Getting loan associates for ${loanId}`);

      const response = await this.authService.makeApiCall<EncompassLoanAssociate[]>(
        `${this.baseUrl}/encompass/v1/loans/${loanId}/associates`,
        { method: 'GET' },
        `Getting loan associates for ${loanId}`
      );

      return response;

    } catch (error) {
      this.logger.warn(`Could not get loan associates for ${loanId}`, { error });
      // Return null if loan associates are not accessible
      return null;
    }
  }

  /**
   * Search for business contacts (realtors) by name
   */
  public async searchBusinessContacts(name: string): Promise<EncompassBusinessContact[]> {
    try {
      this.logger.debug(`Searching business contacts for: ${name}`);

      const response = await this.authService.makeApiCall<{ businessContacts: EncompassBusinessContact[] }>(
        `${this.baseUrl}/encompass/v1/businessContacts`,
        {
          method: 'GET',
          params: {
            filter: `name eq "${name}"`,
            limit: 10
          }
        },
        `Searching business contacts for ${name}`
      );

      return response.businessContacts || [];

    } catch (error) {
      this.logger.warn(`Could not search business contacts for ${name}`, { error });
      return [];
    }
  }

  /**
   * Create comprehensive record directly from batch contact data
   */
  public createComprehensiveRecordFromContact(
    batchContact: EncompassBatchContact
  ): ComprehensiveBorrowerRecord {
    return {
      borrower: {
        id: batchContact.contactId,
        name: batchContact.fullName,
        firstName: batchContact.firstName,
        lastName: batchContact.lastName,
        email: batchContact.personalEmail || batchContact.businessEmail || undefined,
        phones: {
          home: batchContact.homePhone || undefined,
          work: batchContact.workPhone || undefined,
          mobile: batchContact.mobilePhone || undefined
        },
        dateOfBirth: batchContact.dateOfBirth, // May be undefined, can fetch individually if needed
        address: batchContact.propertyAddress ? {
          street1: batchContact.propertyAddress.street,
          street2: undefined,
          city: batchContact.propertyAddress.city,
          state: batchContact.propertyAddress.state,
          zip: batchContact.propertyAddress.zipCode
        } : undefined,
        employer: {
          name: batchContact.employerName || undefined,
          jobTitle: batchContact.jobTitle || undefined
        },
        referralText: batchContact.referralText // Contains realtor info like "Guillermo A Gonzalez realtor"
      },

      // Extract loan information from batch contact
      loan: this.extractLoanInfoFromBatchContact(batchContact),

      // Extract realtor information from batch contact
      realtor: this.extractRealtorInfoFromBatchContact(batchContact),

      dataSources: {
        borrowerContact: true,
        loanData: !!batchContact.loanId,
        loanAssociates: false,
        realtorBusinessContact: false
      },

      metadata: {
        extractedAt: new Date().toISOString(),
        source: 'encompass-batch-contacts',
        version: '2.0'
      }
    };
  }

  /**
   * Extract loan information from batch contact
   */
  private extractLoanInfoFromBatchContact(contact: EncompassBatchContact): {
    id?: string;
    guid?: string;
    interestRate?: number;
    closingDate?: string;
    propertyAddress?: string;
    amount?: number;
    type?: string;
  } | undefined {
    if (!contact.loanId) {
      return undefined;
    }

    // Parse numeric values, handling empty strings
    const loanAmount = contact.loanAmount && contact.loanAmount !== '' ? parseFloat(contact.loanAmount) : undefined;
    const interestRate = contact.interestRate && contact.interestRate !== '' ? parseFloat(contact.interestRate) : undefined;
    const closingDate = contact.closingDate && contact.closingDate !== '' ? contact.closingDate : undefined;

    return {
      id: contact.loanId,
      guid: contact.loanId,
      interestRate: (interestRate !== undefined && !isNaN(interestRate)) ? interestRate : undefined,
      closingDate,
      propertyAddress: contact.propertyAddress?.fullAddress,
      amount: (loanAmount !== undefined && !isNaN(loanAmount)) ? loanAmount : undefined,
      type: undefined // Not available in batch contact
    };
  }

  /**
   * Extract realtor information from batch contact
   */
  private extractRealtorInfoFromBatchContact(contact: EncompassBatchContact): {
    name?: string;
    phone?: string;
    email?: string;
    businessContactId?: string;
  } | undefined {
    const hasRealtorInfo = contact.realtorName || contact.realtorPhone || contact.realtorEmail || contact.referralText;

    if (!hasRealtorInfo) {
      return undefined;
    }

    return {
      name: contact.realtorName || contact.referralText || undefined,
      phone: contact.realtorPhone || undefined,
      email: contact.realtorEmail || undefined,
      businessContactId: undefined
    };
  }

  /**
   * Extract comprehensive data from multiple sources (legacy method with API calls)
   */
  public async extractComprehensiveData(
    borrowerContact: EncompassBorrowerContact,
    loanId?: string
  ): Promise<ComprehensiveBorrowerRecord> {
    this.logger.debug(`Extracting comprehensive data for contact ${borrowerContact.id}`);

    // Get additional data if loan ID is available
    let loanData: EncompassLoanData | null = null;
    let loanAssociates: EncompassLoanAssociate[] | null = null;

    if (loanId) {
      try {
        [loanData, loanAssociates] = await Promise.all([
          this.getLoanData(loanId),
          this.getLoanAssociates(loanId)
        ]);
      } catch (error) {
        this.logger.warn(`Could not get additional loan data for ${loanId}`, { error });
      }
    }

    // Search for realtor business contact if referral name exists
    let realtorBusinessContact: EncompassBusinessContact | null = null;
    if (borrowerContact.referral) {
      try {
        const businessContacts = await this.searchBusinessContacts(borrowerContact.referral);
        realtorBusinessContact = businessContacts[0] || null;
      } catch (error) {
        this.logger.warn(`Could not find business contact for realtor ${borrowerContact.referral}`, { error });
      }
    }

    return this.buildComprehensiveRecord(borrowerContact, loanData, loanAssociates, realtorBusinessContact);
  }

  /**
   * Build comprehensive borrower record from all available data
   */
  private buildComprehensiveRecord(
    borrowerContact: EncompassBorrowerContact,
    loanData: EncompassLoanData | null = null,
    loanAssociates: EncompassLoanAssociate[] | null = null,
    realtorBusinessContact: EncompassBusinessContact | null = null
  ): ComprehensiveBorrowerRecord {
    const comprehensiveRecord: ComprehensiveBorrowerRecord = {
      // Borrower Information
      borrower: {
        id: borrowerContact.id,
        name: `${borrowerContact.firstName || ''} ${borrowerContact.lastName || ''}`.trim(),
        firstName: borrowerContact.firstName,
        lastName: borrowerContact.lastName,
        email: borrowerContact.personalEmail || borrowerContact.businessEmail,
        phones: {
          home: borrowerContact.homePhone,
          work: borrowerContact.workPhone,
          mobile: borrowerContact.mobilePhone
        },
        dateOfBirth: borrowerContact.birthdate,
        address: borrowerContact.currentMailingAddress,
        employer: {
          name: borrowerContact.employerName,
          jobTitle: borrowerContact.jobTitle
        }
      },
      
      // Loan Information
      loan: loanData ? {
        id: loanData.loanNumber,
        guid: loanData.guid,
        interestRate: (this.extractFieldValue(loanData, ENCOMPASS_FIELD_IDS.INTEREST_RATE) as number) || loanData.interestRate,
        closingDate: (this.extractFieldValue(loanData, ENCOMPASS_FIELD_IDS.CLOSING_DATE) as string) || loanData.closingDate,
        propertyAddress: this.formatPropertyAddress(loanData),
        amount: (this.extractFieldValue(loanData, ENCOMPASS_FIELD_IDS.LOAN_AMOUNT) as number) || loanData.loanAmount,
        type: (this.extractFieldValue(loanData, ENCOMPASS_FIELD_IDS.LOAN_TYPE) as string) || loanData.loanType
      } : undefined,
      
      // Realtor Information
      realtor: {
        name: borrowerContact.referral || realtorBusinessContact?.name,
        phone: realtorBusinessContact?.phone,
        email: realtorBusinessContact?.email,
        businessContactId: realtorBusinessContact?.id
      },
      
      // Data source tracking
      dataSources: {
        borrowerContact: true,
        loanData: !!loanData,
        loanAssociates: !!loanAssociates,
        realtorBusinessContact: !!realtorBusinessContact
      },

      // Metadata
      metadata: {
        extractedAt: new Date().toISOString(),
        source: 'Encompass API',
        version: '1.0'
      }
    };

    // Extract loan originator information from associates
    if (loanAssociates && Array.isArray(loanAssociates)) {
      const loanOriginator = loanAssociates.find(associate => 
        associate.roleType === 'LoanOfficer' || 
        associate.roleType === 'LoanOriginator' ||
        associate.role?.toLowerCase().includes('originator') ||
        associate.role?.toLowerCase().includes('officer')
      );
      
      if (loanOriginator && comprehensiveRecord.loan) {
        comprehensiveRecord.loan.loanOriginator = {
          name: loanOriginator.name || `${loanOriginator.firstName || ''} ${loanOriginator.lastName || ''}`.trim(),
          email: loanOriginator.email,
          phone: loanOriginator.phone,
          role: loanOriginator.role || loanOriginator.roleType,
          id: loanOriginator.id
        };
      }
    }

    return comprehensiveRecord;
  }



  /**
   * Extract field value from loan data using field ID
   */
  private extractFieldValue(loanData: EncompassLoanData, fieldId: string): unknown {
    return loanData.fields?.[fieldId];
  }

  /**
   * Format property address from loan data
   */
  private formatPropertyAddress(loanData: EncompassLoanData): string | undefined {
    const address = loanData.propertyAddress || loanData.subjectPropertyAddress;
    if (!address) return undefined;

    return `${address.street1 || ''} ${address.street2 || ''}, ${address.city || ''}, ${address.state || ''} ${address.zip || ''}`.trim();
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get authentication service for external use
   */
  public getAuthService(): EncompassAuthService {
    return this.authService;
  }
}
