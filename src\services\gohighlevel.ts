/**
 * GoHighLevel API Service
 * Handles all interactions with GoHighLevel CRM API
 */

import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Logger } from '../utils/simple-logger';
import { ComprehensiveBorrowerRecord } from '../types/integration';

export interface GoHighLevelConfig {
  apiUrl: string;
  apiKey: string;
  locationId: string;
  pipelineId?: string;
  pipelineStageId?: string;
}

export interface GHLContact {
  id?: string;
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  phone: string;
  address1?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  source?: string;
  customFields?: Array<{
    key: string;
    field_value: string;
  }>;
  tags?: string[];
}

export interface GHLOpportunity {
  id?: string;
  pipelineId: string;
  pipelineStageId: string;
  locationId: string;
  name: string;
  status: string;
  contactId: string;
  monetaryValue?: number;
  customFields?: Array<{
    key: string;
    field_value: string;
  }>;
}

export interface GHLContactResult {
  action: 'created' | 'updated';
  contact: any;
  contactId: string;
}

export interface GHLOpportunityResult {
  success: boolean;
  opportunityId: string;
  isNew: boolean;
  message: string;
}

export class GoHighLevelService {
  private logger: Logger;
  private config: GoHighLevelConfig;
  private apiCallCount = 0;
  private errors: Array<{ description: string; error: any }> = [];

  constructor(config: GoHighLevelConfig) {
    this.config = config;
    this.logger = new Logger('GoHighLevelService');
  }

  /**
   * Make API call with error handling and rate limiting
   */
  private async makeApiCall<T = any>(
    url: string,
    options: AxiosRequestConfig,
    description: string
  ): Promise<T> {
    this.apiCallCount++;
    
    try {
      this.logger.debug(`Making GHL API call: ${description}`, { 
        url, 
        method: options.method || 'GET' 
      });

      const response: AxiosResponse<T> = await axios(url, options);
      
      // Rate limiting - wait 200ms between requests
      await new Promise(resolve => setTimeout(resolve, 200));
      
      this.logger.debug(`GHL API call successful: ${description}`, {
        statusCode: response.status
      });

      return response.data;

    } catch (error: any) {
      this.logger.error(`GHL API call failed: ${description}`, { 
        error: error.response?.data || error.message,
        url, 
        method: options.method 
      });
      throw error;
    }
  }

  /**
   * Transform Encompass data to GoHighLevel format
   */
  public transformToGHLFormat(encompassContact: ComprehensiveBorrowerRecord): GHLContact {
    const borrower = encompassContact.borrower;
    const phones = [];

    if (borrower.phones?.home) phones.push(borrower.phones.home);
    if (borrower.phones?.work) phones.push(borrower.phones.work);
    if (borrower.phones?.mobile) phones.push(borrower.phones.mobile);

    // Primary phone (first available)
    const primaryPhone = phones[0] || '';

    // Full address
    const address = borrower.address;
    const fullAddress = address ?
      `${address.street1 || ''} ${address.street2 || ''}, ${address.city || ''}, ${address.state || ''} ${address.zip || ''}`.trim() : '';

    return {
      // Standard GoHighLevel fields
      firstName: borrower.firstName || '',
      lastName: borrower.lastName || '',
      name: `${borrower.firstName || ''} ${borrower.lastName || ''}`.trim(),
      email: borrower.email || '',
      phone: primaryPhone,
      address1: address?.street1 || '',
      city: address?.city || '',
      state: address?.state || '',
      postalCode: address?.zip || '',
      country: 'US', // Default to US
      source: 'Encompass API',

      // Custom fields for additional borrower data
      customFields: [
        { key: 'encompass_contact_id', field_value: borrower.id || '' },
        { key: 'date_of_birth', field_value: borrower.dateOfBirth || '' },
        { key: 'home_phone', field_value: borrower.phones?.home || '' },
        { key: 'work_phone', field_value: borrower.phones?.work || '' },
        { key: 'mobile_phone', field_value: borrower.phones?.mobile || '' },
        { key: 'business_email', field_value: borrower.email || '' },
        { key: 'personal_email', field_value: borrower.email || '' },
        { key: 'full_address', field_value: fullAddress },
        { key: 'realtor_name', field_value: encompassContact.realtor?.name || '' },
        { key: 'import_date', field_value: new Date().toISOString() },
        { key: 'borrower_type', field_value: 'Primary Borrower' },

        // Placeholder fields for loan data (when API access is available)
        { key: 'interest_rate', field_value: '' },
        { key: 'closing_date', field_value: '' },
        { key: 'loan_originator', field_value: '' },
        { key: 'realtor_phone', field_value: '' },
        { key: 'realtor_email', field_value: '' },
        { key: 'property_address', field_value: '' },
        { key: 'loan_amount', field_value: '' },
        { key: 'loan_type', field_value: '' }
      ].filter(field => field.field_value !== ''), // Remove empty custom fields

      // Tags for organization and tracking
      tags: ['Encompass Import', 'Mortgage Lead', 'Borrower', 'Real Estate']
    };
  }

  /**
   * Search for contacts in GoHighLevel
   */
  public async searchGoHighLevelContact(searchParams: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
  }): Promise<any> {
    this.logger.debug('Searching for contact in GoHighLevel', { searchParams });

    const url = `${this.config.apiUrl}/contacts/search`;
    const headers = {
      'Accept': 'application/json',
      'Authorization': `Bearer ${this.config.apiKey}`,
      'Content-Type': 'application/json',
      'Version': '2021-07-28'
    };

    // Prepare search request body
    const requestBody = {
      locationId: this.config.locationId,
      pageLimit: 10,
      filters: [] as any[]
    };

    // Add filters based on provided search parameters
    if (searchParams.firstName) {
      const trimmedFirstName = searchParams.firstName.trim();
      requestBody.filters.push({
        field: 'firstNameLowerCase',
        operator: 'contains',
        value: trimmedFirstName.toLowerCase()
      });
    }

    if (searchParams.lastName) {
      const trimmedLastName = searchParams.lastName.trim();
      requestBody.filters.push({
        field: 'lastNameLowerCase',
        operator: 'contains',
        value: trimmedLastName.toLowerCase()
      });
    }

    if (searchParams.email) {
      requestBody.filters.push({
        field: 'email',
        operator: 'eq',
        value: searchParams.email
      });
    }

    if (searchParams.phone) {
      // Remove any non-digit characters from phone number for comparison
      const cleanPhone = searchParams.phone.replace(/\D/g, '');
      if (cleanPhone) {
        requestBody.filters.push({
          field: 'phone',
          operator: 'contains',
          value: cleanPhone
        });
      }
    }

    try {
      const response = await this.makeApiCall(url, {
        method: 'POST',
        headers,
        data: requestBody
      }, `Searching for contact with filters: ${JSON.stringify(searchParams)}`);

      if (response && response.contacts && response.contacts.length > 0) {
        this.logger.debug(`Found ${response.contacts.length} matching contacts in GoHighLevel`);
        return response.contacts[0];
      }

      this.logger.debug('No matching contacts found in GoHighLevel');
      return null;
    } catch (error) {
      this.logger.error('Error searching for contact in GoHighLevel', { error });
      return null;
    }
  }

  /**
   * Check for duplicate contacts using the dedicated API
   */
  public async checkDuplicateContact(contactData: GHLContact): Promise<string | null> {
    // Trim whitespace from names
    const trimmedFirstName = contactData.firstName ? contactData.firstName.trim() : '';
    const trimmedLastName = contactData.lastName ? contactData.lastName.trim() : '';

    this.logger.debug(`Checking for duplicate contact using dedicated API for "${trimmedFirstName}" "${trimmedLastName}"`);

    const baseUrl = `${this.config.apiUrl}/contacts/search/duplicate?locationId=${this.config.locationId}`;
    const headers = {
      'Accept': 'application/json',
      'Authorization': `Bearer ${this.config.apiKey}`,
      'Version': '2021-07-28'
    };

    try {
      // Only try with email if it exists and is not empty
      if (contactData.email && contactData.email.trim()) {
        // Properly encode email, ensuring + and @ are correctly encoded
        const encodedEmail = encodeURIComponent(contactData.email.trim())
          .replace(/\+/g, '%2B')
          .replace(/@/g, '%40');

        const emailUrl = `${baseUrl}&email=${encodedEmail}`;
        this.logger.debug(`Checking duplicate by email: ${contactData.email}`);

        const emailResponse = await this.makeApiCall(emailUrl, { 
          method: 'GET',
          headers 
        }, `Checking duplicate by email: ${contactData.email}`);

        if (emailResponse && emailResponse.contact && emailResponse.contact.id) {
          this.logger.debug(`Found duplicate contact by email with ID: ${emailResponse.contact.id}`);
          return emailResponse.contact.id;
        }
      }

      this.logger.debug('No duplicate contacts found using dedicated API');
      return null;
    } catch (error) {
      this.logger.error('Error checking for duplicate contact', { error });
      return null;
    }
  }

  /**
   * Create or update contact in GoHighLevel
   */
  public async createOrUpdateGHLContact(contactData: GHLContact): Promise<GHLContactResult | null> {
    try {
      // First, try to find existing contact using the dedicated duplicate check API
      const existingContactId = await this.checkDuplicateContact(contactData);

      // Prepare contact data for GoHighLevel
      const ghlContactData = {
        locationId: this.config.locationId,
        ...contactData
      };

      if (existingContactId) {
        // Update existing contact - remove locationId for updates
        const updateData = { ...contactData };
        delete (updateData as any).locationId;

        const updatedContact = await this.makeApiCall(
          `${this.config.apiUrl}/contacts/${existingContactId}`,
          {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${this.config.apiKey}`,
              'Content-Type': 'application/json',
              'Version': '2021-07-28'
            },
            data: updateData
          },
          `Updating existing GHL contact: ${contactData.firstName} ${contactData.lastName}`
        );

        return { action: 'updated', contact: updatedContact, contactId: existingContactId };
      } else {
        // Create new contact - include locationId for creation
        const newContact = await this.makeApiCall(
          `${this.config.apiUrl}/contacts/`,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.config.apiKey}`,
              'Content-Type': 'application/json',
              'Version': '2021-07-28'
            },
            data: ghlContactData
          },
          `Creating new GHL contact: ${contactData.firstName} ${contactData.lastName}`
        );

        return { action: 'created', contact: newContact, contactId: newContact?.contact?.id };
      }

    } catch (error: any) {
      this.logger.error('Error creating/updating GHL contact', {
        error: error.response?.data || error.message
      });
      this.errors.push({
        description: `GHL contact operation for ${contactData.firstName} ${contactData.lastName}`,
        error: error.response?.data || error.message
      });
      return null;
    }
  }

  /**
   * Create opportunity in GoHighLevel
   */
  public async createOpportunity(contactId: string, contactData: GHLContact): Promise<GHLOpportunityResult | null> {
    // Validate that we have a contactId before proceeding
    if (!contactId) {
      this.logger.error('Cannot create opportunity: Missing contactId');
      return null;
    }

    if (!this.config.pipelineId || !this.config.pipelineStageId) {
      this.logger.warn('Cannot create opportunity: Missing pipeline configuration');
      return null;
    }

    this.logger.debug(`Creating opportunity for contact ID: ${contactId}`);

    try {
      // Create opportunity directly (no searching due to API limitations)
      this.logger.debug(`Creating new opportunity for contact ID: ${contactId}`);

      const opportunityData: GHLOpportunity = {
        pipelineId: this.config.pipelineId,
        pipelineStageId: this.config.pipelineStageId,
        locationId: this.config.locationId,
        name: `${contactData.firstName || ''} ${contactData.lastName || ''} - Mortgage Lead`.trim(),
        status: "open",
        contactId,
        monetaryValue: 0, // Default value, can be updated later
        customFields: [
          { key: 'source', field_value: 'Encompass API' },
          { key: 'lead_type', field_value: 'Mortgage Borrower' },
          { key: 'import_date', field_value: new Date().toISOString() },
          { key: 'encompass_contact_id', field_value: contactData.customFields?.find(f => f.key === 'encompass_contact_id')?.field_value || '' }
        ].filter(field => field.field_value !== '') // Remove empty custom fields
      };

      const response = await this.makeApiCall(
        `${this.config.apiUrl}/opportunities/`,
        {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
            'Version': '2021-07-28'
          },
          data: opportunityData
        },
        `Creating opportunity for ${contactData.firstName} ${contactData.lastName}`
      );

      const newOpportunityId = response?.opportunity?.id;
      this.logger.debug(`Created new opportunity with ID: ${newOpportunityId}`);

      return {
        success: true,
        opportunityId: newOpportunityId,
        isNew: true,
        message: 'New opportunity created'
      };

    } catch (error: any) {
      const errorData = error.response?.data;
      this.logger.error('Failed to create opportunity', { error: errorData || error.message });

      // Check if this is a duplicate opportunity error
      if (errorData?.statusCode === 400 && (errorData?.message?.includes('duplicate') || errorData?.message?.includes('Can not create duplicate'))) {
        this.logger.debug('Duplicate opportunity detected, using existing one');

        return {
          success: true,
          opportunityId: 'existing',
          isNew: false,
          message: 'Duplicate opportunity detected'
        };
      }

      this.errors.push({
        description: `Opportunity creation for ${contactData.firstName} ${contactData.lastName}`,
        error: errorData || error.message
      });
      return null;
    }
  }

  /**
   * Get API call count
   */
  public getApiCallCount(): number {
    return this.apiCallCount;
  }

  /**
   * Get errors
   */
  public getErrors(): Array<{ description: string; error: any }> {
    return this.errors;
  }

  /**
   * Reset counters
   */
  public resetCounters(): void {
    this.apiCallCount = 0;
    this.errors = [];
  }
}
