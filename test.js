/**
 * Test Script for Encompass to GoHighLevel Integration
 * Run with: node test.js
 */

// Load environment variables
require('dotenv').config();

const { EncompassGHLSyncService } = require('./dist/services/encompass-ghl-sync');

async function runTest() {
    console.log('🚀 Starting Encompass to GoHighLevel Integration Test...\n');

    try {
        // Configuration from environment variables
        const config = {
            encompass: {
                baseUrl: process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com',
                username: process.env.ENCOMPASS_USERNAME,
                password: process.env.ENCOMPASS_PASSWORD,
                clientId: process.env.ENCOMPASS_CLIENT_ID,
                clientSecret: process.env.ENCOMPASS_CLIENT_SECRET,
                instance: process.env.ENCOMPASS_INSTANCE
            },
            gohighlevel: {
                apiUrl: process.env.GHL_API_URL || 'https://services.leadconnectorhq.com',
                apiKey: process.env.GHL_API_KEY,
                locationId: process.env.GHL_LOCATION_ID,
                pipelineId: process.env.GHL_PIPELINE_ID,
                pipelineStageId: process.env.GHL_PIPELINE_STAGE_ID
            }
        };

        // Validate configuration
        console.log('🔧 Validating configuration...');
        const missingConfig = [];

        // Check Encompass config
        if (!config.encompass.username) missingConfig.push('ENCOMPASS_USERNAME');
        if (!config.encompass.password) missingConfig.push('ENCOMPASS_PASSWORD');
        if (!config.encompass.clientId) missingConfig.push('ENCOMPASS_CLIENT_ID');
        if (!config.encompass.clientSecret) missingConfig.push('ENCOMPASS_CLIENT_SECRET');
        if (!config.encompass.instance) missingConfig.push('ENCOMPASS_INSTANCE');

        // Check GoHighLevel config
        if (!config.gohighlevel.apiKey) missingConfig.push('GHL_API_KEY');
        if (!config.gohighlevel.locationId) missingConfig.push('GHL_LOCATION_ID');

        if (missingConfig.length > 0) {
            console.error('❌ Missing required environment variables:');
            missingConfig.forEach(key => console.error(`   - ${key}`));
            console.error('\nPlease check your .env file and ensure all required variables are set.');
            process.exit(1);
        }

        console.log('✅ Configuration validated successfully');
        console.log(`   📍 Encompass Instance: ${config.encompass.instance}`);
        console.log(`   📍 GHL Location: ${config.gohighlevel.locationId}`);
        if (config.gohighlevel.pipelineId) {
            console.log(`   📍 GHL Pipeline: ${config.gohighlevel.pipelineId}`);
        }
        console.log();

        // Create sync service
        const syncService = new EncompassGHLSyncService(config);

        // Run integration with a small number of contacts for testing
        const contactLimit = 5; // Start with 5 contacts for testing
        console.log(`🔄 Running integration for ${contactLimit} contacts...\n`);

        const result = await syncService.runIntegration(contactLimit);

        // Display results
        console.log('\n🎉 Integration test completed!');
        console.log('\n📊 === FINAL RESULTS ===');
        console.log(`⏱️  Total time: ${result.elapsedTime.toFixed(2)} seconds`);
        console.log(`📞 API calls made: ${result.apiCallCount}`);
        console.log(`👥 Total contacts processed: ${result.totalProcessed}`);
        console.log(`✅ Contacts created: ${result.contactsCreated}`);
        console.log(`🔄 Contacts updated: ${result.contactsUpdated}`);
        console.log(`🎯 Opportunities created: ${result.opportunitiesCreated}`);
        console.log(`🔍 Opportunities found: ${result.opportunitiesFound}`);
        console.log(`❌ Failed contacts: ${result.failedContacts}`);
        console.log(`🚨 Total errors: ${result.errors.length}`);

        if (result.errors.length > 0) {
            console.log('\n🚨 Errors encountered:');
            result.errors.forEach((error, index) => {
                console.log(`  ${index + 1}. ${error.description}:`);
                console.log(`     ${JSON.stringify(error.error, null, 2)}`);
            });
        }

        if (result.processedContacts.length > 0) {
            console.log('\n👥 Processed contacts:');
            result.processedContacts.forEach((contact, index) => {
                const status = contact.ghlContactId ? '✅' : '❌';
                const oppStatus = contact.ghlOpportunityId ? '🎯' : '⚪';
                console.log(`  ${index + 1}. ${status} ${contact.name}`);
                console.log(`     📧 ${contact.email || 'No email'}`);
                console.log(`     📞 ${contact.phone || 'No phone'}`);
                console.log(`     🆔 GHL Contact: ${contact.ghlContactId || 'Failed'}`);
                console.log(`     ${oppStatus} Opportunity: ${contact.ghlOpportunityId || 'None'}`);
                console.log(`     🔄 Action: ${contact.action} | Opp: ${contact.opportunityAction}`);
                console.log();
            });
        }

        // Success/failure determination
        const successRate = result.totalProcessed > 0 ? 
            ((result.contactsCreated + result.contactsUpdated) / result.totalProcessed * 100).toFixed(1) : 0;

        console.log(`📈 Success rate: ${successRate}%`);

        if (result.failedContacts === 0 && result.errors.length === 0) {
            console.log('\n🎉 Test completed successfully with no errors!');
            console.log('✅ The integration is working correctly and ready for production use.');
        } else if (successRate >= 80) {
            console.log('\n⚠️  Test completed with some issues, but mostly successful.');
            console.log('💡 Review the errors above and consider if they need to be addressed.');
        } else {
            console.log('\n❌ Test completed with significant issues.');
            console.log('🔧 Please review and fix the errors before using in production.');
        }

        console.log('\n💡 Next steps:');
        console.log('   1. Review the results above');
        console.log('   2. Check the contacts in your GoHighLevel account');
        console.log('   3. If successful, you can increase the contactLimit for larger syncs');
        console.log('   4. Consider setting up automated syncing in the main application');

    } catch (error) {
        console.error('\n💥 Test failed with error:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Test interrupted by user');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Test terminated');
    process.exit(0);
});

// Run the test
if (require.main === module) {
    runTest().catch(error => {
        console.error('💥 Unhandled error:', error);
        process.exit(1);
    });
}

module.exports = { runTest };
