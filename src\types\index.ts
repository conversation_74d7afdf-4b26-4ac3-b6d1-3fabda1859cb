/**
 * Type Definitions Index
 * Exports all type definitions for the integration system
 */

// Encompass Types
export * from './encompass';

// Integration Types
export * from './integration';

// MongoDB Types
export * from './mongodb';

// Common utility types
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type PartialFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Generic API types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface TimestampedEntity {
  createdAt: string;
  updatedAt: string;
}

export interface IdentifiableEntity {
  id: string;
}

// Error handling types
export interface ErrorContext {
  operation: string;
  timestamp: string;
  metadata?: Record<string, unknown>;
}

export interface RetryableError extends Error {
  retryable: boolean;
  retryAfter?: number;
}

// Configuration validation types
export interface ConfigValidationError {
  field: string;
  message: string;
  value?: unknown;
}

export interface ConfigValidationResult {
  valid: boolean;
  errors: ConfigValidationError[];
}

// Logging types
export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: Record<string, unknown>;
  error?: Error;
}

// Rate limiting types
export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  retryAfter?: number;
}

export interface RateLimitStatus {
  remaining: number;
  reset: number;
  limit: number;
}

// Monitoring and alerting types
export interface MonitoringAlert {
  id: string;
  name: string;
  severity: AlertSeverity;
  message: string;
  timestamp: string;
  resolved: boolean;
}

export interface SystemMetrics {
  cpu: number;
  memory: number;
  disk: number;
  uptime: number;
}

export interface PerformanceMetrics {
  responseTime: number;
  throughput: number;
  errorRate: number;
}

export interface AlertRule {
  name: string;
  metric: string;
  operator: 'greater_than' | 'less_than' | 'equals';
  threshold: number;
  severity: AlertSeverity;
  message: string;
}

export type AlertSeverity = 'info' | 'warning' | 'critical';

export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck: string;
  responseTime?: number;
  error?: string;
}

export interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: {
    encompass: ServiceHealth;
    mongodb: ServiceHealth;
    sync: ServiceHealth;
  };
  metrics: {
    uptime: number;
    memoryUsage: number;
    lastSyncTime?: string;
  };
  alerts?: MonitoringAlert[];
}
