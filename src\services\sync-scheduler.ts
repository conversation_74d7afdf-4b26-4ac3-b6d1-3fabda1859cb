/**
 * Sync Scheduler Service
 * Implements automated synchronization between Encompass, MongoDB, and GoHighLevel
 */

import * as cron from 'node-cron';
import { v4 as uuidv4 } from 'uuid';
import { EncompassBorrowerService } from './encompass-borrower';
import { MongoContactService } from './mongo-contact';
import { SyncStatus, SyncLogDocument } from '../types/mongodb';

import { Logger } from '../utils/simple-logger';

export interface SyncSchedulerConfig {
  cronExpression: string; // Default: '0 * * * *' (every hour)
  batchSize: number;
  maxConcurrentSyncs: number;
  enableAutoSync: boolean;
  syncTimeoutMs: number;
  retryDelayMs: number;
  maxRetries: number;
}

export interface SyncResult {
  syncId: string;
  success: boolean;
  totalProcessed: number;
  newContacts: number;
  updatedContacts: number;
  syncedToGHL: number;
  failedContacts: number;
  duration: number;
  errors: string[];
}

export class SyncSchedulerService {
  private encompassService: EncompassBorrowerService;
  private mongoService: MongoContactService;
  private logger: Logger;
  private config: SyncSchedulerConfig;
  private cronJob: cron.ScheduledTask | null = null;
  private isRunning: boolean = false;
  private currentSyncId: string | null = null;

  constructor(
    encompassService: EncompassBorrowerService,
    mongoService: MongoContactService,
    config: SyncSchedulerConfig,
    logger: Logger
  ) {
    this.encompassService = encompassService;
    this.mongoService = mongoService;
    this.config = config;
    this.logger = logger;
  }

  /**
   * Start the sync scheduler
   */
  public start(): void {
    if (!this.config.enableAutoSync) {
      this.logger.info('Auto sync is disabled');
      return;
    }

    if (this.cronJob) {
      this.logger.warn('Sync scheduler is already running');
      return;
    }

    this.logger.info(`Starting sync scheduler with cron expression: ${this.config.cronExpression}`);

    this.cronJob = cron.schedule(this.config.cronExpression, async () => {
      await this.runSync();
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.cronJob.start();
    this.logger.info('Sync scheduler started successfully');
  }

  /**
   * Stop the sync scheduler
   */
  public stop(): void {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
      this.logger.info('Sync scheduler stopped');
    }
  }

  /**
   * Run a manual sync
   */
  public async runManualSync(): Promise<SyncResult> {
    this.logger.info('Starting manual sync...');
    return await this.runSync();
  }

  /**
   * Main sync execution logic
   */
  private async runSync(): Promise<SyncResult> {
    if (this.isRunning) {
      this.logger.warn('Sync is already running, skipping this execution');
      throw new Error('Sync already in progress');
    }

    const syncId = uuidv4();
    this.currentSyncId = syncId;
    this.isRunning = true;

    const startTime = Date.now();
    let syncLog: SyncLogDocument;

    try {
      this.logger.info(`Starting sync operation: ${syncId}`);

      // Create sync log
      syncLog = await this.mongoService.createSyncLog(syncId);

      // Step 1: Fetch latest contacts from Encompass
      this.logger.info('Fetching contacts from Encompass...');
      const encompassContacts = await this.encompassService.fetchComprehensiveBorrowerData(
        this.config.batchSize,
        0
      );

      this.logger.info(`Fetched ${encompassContacts.length} contacts from Encompass`);

      // Step 2: Save to MongoDB with change detection
      this.logger.info('Saving contacts to MongoDB with change detection...');
      const changeResult = await this.mongoService.saveContacts(
        encompassContacts.map(contact => ({
          contact,
          encompassId: contact.borrower.id
        }))
      );

      this.logger.info('Change detection completed', {
        new: changeResult.newContacts.length,
        updated: changeResult.updatedContacts.length,
        unchanged: changeResult.unchangedContacts.length
      });

      // Step 3: Get contacts that need to be synced to GoHighLevel
      const contactsForSync = await this.mongoService.getContactsForSync(this.config.batchSize);
      this.logger.info(`Found ${contactsForSync.length} contacts that need to be synced to GoHighLevel`);

      // Step 4: Sync to GoHighLevel (placeholder - implement based on your GHL service)
      let syncedToGHL = 0;
      let failedContacts = 0;
      const errors: string[] = [];

      for (const contact of contactsForSync) {
        try {
          // Mark as in progress
          await this.mongoService.getContactsCollection().updateOne(
            { encompassId: contact.encompassId },
            { $set: { syncStatus: SyncStatus.IN_PROGRESS } }
          );

          // TODO: Implement actual GoHighLevel sync here
          // const ghlContactId = await this.ghlService.createOrUpdateContact(contact);
          const ghlContactId = `ghl_${Date.now()}_${Math.random()}`; // Placeholder

          await this.mongoService.markContactSynced(contact.encompassId, ghlContactId);
          syncedToGHL++;

          this.logger.debug(`Synced contact to GHL: ${contact.encompassId} -> ${ghlContactId}`);

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          await this.mongoService.markContactSyncFailed(contact.encompassId, errorMessage);
          errors.push(`${contact.encompassId}: ${errorMessage}`);
          failedContacts++;

          this.logger.error(`Failed to sync contact ${contact.encompassId}`, { error });
        }
      }

      const duration = Date.now() - startTime;

      // Update sync log
      await this.mongoService.updateSyncLog(syncId, {
        status: SyncStatus.COMPLETED,
        totalContacts: encompassContacts.length,
        newContacts: changeResult.newContacts.length,
        updatedContacts: changeResult.updatedContacts.length,
        syncedToGHL,
        failedContacts,
        errors: errors.map(error => ({
          encompassId: error.split(':')[0] || 'unknown',
          contactName: 'Unknown',
          error: error.split(':')[1] || error,
          timestamp: new Date(),
          retryCount: 0
        })),
        duration
      });

      const result: SyncResult = {
        syncId,
        success: true,
        totalProcessed: encompassContacts.length,
        newContacts: changeResult.newContacts.length,
        updatedContacts: changeResult.updatedContacts.length,
        syncedToGHL,
        failedContacts,
        duration,
        errors
      };

      this.logger.info('Sync completed successfully', { result });
      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.logger.error('Sync failed', { error, syncId, duration });

      // Update sync log with failure
      if (syncLog!) {
        await this.mongoService.updateSyncLog(syncId, {
          status: SyncStatus.FAILED,
          errors: [{
            encompassId: 'SYSTEM',
            contactName: 'SYSTEM',
            error: errorMessage,
            timestamp: new Date(),
            retryCount: 0
          }],
          duration
        });
      }

      const result: SyncResult = {
        syncId,
        success: false,
        totalProcessed: 0,
        newContacts: 0,
        updatedContacts: 0,
        syncedToGHL: 0,
        failedContacts: 0,
        duration,
        errors: [errorMessage]
      };

      return result;

    } finally {
      this.isRunning = false;
      this.currentSyncId = null;
    }
  }

  /**
   * Get current sync status
   */
  public getSyncStatus(): {
    isRunning: boolean;
    currentSyncId: string | null;
    isScheduled: boolean;
    nextRun?: Date;
  } {
    return {
      isRunning: this.isRunning,
      currentSyncId: this.currentSyncId,
      isScheduled: this.cronJob !== null,
      nextRun: this.cronJob ? new Date() : undefined // Note: nextDate() method may not be available in all versions
    };
  }

  /**
   * Update sync configuration
   */
  public updateConfig(newConfig: Partial<SyncSchedulerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart scheduler if cron expression changed
    if (newConfig.cronExpression && this.cronJob) {
      this.stop();
      this.start();
    }

    this.logger.info('Sync scheduler configuration updated', newConfig);
  }

  /**
   * Get sync history
   */
  public async getSyncHistory(limit: number = 10): Promise<SyncLogDocument[]> {
    try {
      return await this.mongoService.getSyncLogsCollection()
        .find({})
        .sort({ startedAt: -1 })
        .limit(limit)
        .toArray();
    } catch (error) {
      this.logger.error('Failed to get sync history', { error });
      throw error;
    }
  }

  /**
   * Cleanup old sync logs
   */
  public async cleanupOldSyncLogs(retentionDays: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const result = await this.mongoService.getSyncLogsCollection().deleteMany({
        startedAt: { $lt: cutoffDate }
      });

      this.logger.info(`Cleaned up ${result.deletedCount} old sync logs`);
      return result.deletedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup old sync logs', { error });
      throw error;
    }
  }
}
