/**
 * Retry Handler Utility
 * Implements comprehensive retry logic with exponential backoff and circuit breaker patterns
 */

import { IntegrationError, RetryableError } from '../types';
import { Logger } from './simple-logger';

export interface RetryConfig {
  maxRetries: number;
  baseDelayMs: number;
  maxDelayMs: number;
  exponentialBase: number;
  jitterMs: number;
  retryableErrors: string[];
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeoutMs: number;
  monitoringPeriodMs: number;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: number;
  totalDuration: number;
}

export class RetryHandler {
  private readonly config: RetryConfig;
  private readonly logger: Logger;

  constructor(config: Partial<RetryConfig> = {}, logger: Logger) {
    this.config = {
      maxRetries: config.maxRetries || 3,
      baseDelayMs: config.baseDelayMs || 1000,
      maxDelayMs: config.maxDelayMs || 30000,
      exponentialBase: config.exponentialBase || 2,
      jitterMs: config.jitterMs || 100,
      retryableErrors: config.retryableErrors || [
        'NETWORK_ERROR',
        'TIMEOUT_ERROR',
        'RATE_LIMITED',
        'SERVER_ERROR',
        'TEMPORARY_ERROR'
      ]
    };
    this.logger = logger;
    this.logger.setContext({ component: 'RetryHandler' });
  }

  /**
   * Execute a function with retry logic
   */
  public async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    customConfig?: Partial<RetryConfig>
  ): Promise<RetryResult<T>> {
    const config = { ...this.config, ...customConfig };
    const startTime = Date.now();
    let lastError: Error | null = null;

    this.logger.debug(`Starting retry operation: ${operationName}`, {
      maxRetries: config.maxRetries,
      baseDelayMs: config.baseDelayMs
    });

    for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
      try {
        const result = await operation();
        const totalDuration = Date.now() - startTime;

        this.logger.debug(`Operation succeeded: ${operationName}`, {
          attempt,
          totalDuration
        });

        return {
          success: true,
          result,
          attempts: attempt,
          totalDuration
        };

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        const totalDuration = Date.now() - startTime;

        this.logger.warn(`Operation failed: ${operationName}`, {
          attempt,
          error: lastError.message,
          totalDuration
        });

        // Check if error is retryable
        if (!this.isRetryableError(lastError) || attempt > config.maxRetries) {
          this.logger.error(`Operation failed permanently: ${operationName}`, {
            attempt,
            error: lastError.message,
            totalDuration,
            retryable: this.isRetryableError(lastError)
          });

          return {
            success: false,
            error: lastError,
            attempts: attempt,
            totalDuration
          };
        }

        // Calculate delay for next attempt
        if (attempt <= config.maxRetries) {
          const delay = this.calculateDelay(attempt, config);
          
          this.logger.info(`Retrying operation: ${operationName}`, {
            attempt,
            nextAttempt: attempt + 1,
            delayMs: delay
          });

          await this.delay(delay);
        }
      }
    }

    // This should never be reached, but included for completeness
    return {
      success: false,
      error: lastError || new Error('Unknown error'),
      attempts: config.maxRetries + 1,
      totalDuration: Date.now() - startTime
    };
  }

  /**
   * Check if an error is retryable
   */
  private isRetryableError(error: Error): boolean {
    // Check if it's an IntegrationError with retryable flag
    if ('retryable' in error && typeof (error as IntegrationError).retryable === 'boolean') {
      return (error as IntegrationError).retryable;
    }

    // Check if it's a RetryableError
    if ('retryable' in error && typeof (error as RetryableError).retryable === 'boolean') {
      return (error as RetryableError).retryable;
    }

    // Check error code against retryable error codes
    if ('code' in error && typeof (error as IntegrationError).code === 'string') {
      return this.config.retryableErrors.includes((error as IntegrationError).code);
    }

    // Check error message for common retryable patterns
    const retryablePatterns = [
      /timeout/i,
      /network/i,
      /connection/i,
      /rate.?limit/i,
      /server.?error/i,
      /service.?unavailable/i,
      /internal.?error/i,
      /bad.?gateway/i,
      /gateway.?timeout/i
    ];

    return retryablePatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * Calculate delay with exponential backoff and jitter
   */
  private calculateDelay(attempt: number, config: RetryConfig): number {
    // Exponential backoff: baseDelay * (exponentialBase ^ (attempt - 1))
    const exponentialDelay = config.baseDelayMs * Math.pow(config.exponentialBase, attempt - 1);
    
    // Add jitter to prevent thundering herd
    const jitter = Math.random() * config.jitterMs;
    
    // Cap at maximum delay
    const totalDelay = Math.min(exponentialDelay + jitter, config.maxDelayMs);
    
    return Math.floor(totalDelay);
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Circuit Breaker Pattern Implementation
 */
export class CircuitBreaker {
  private readonly config: CircuitBreakerConfig;
  private readonly logger: Logger;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private successCount: number = 0;

  constructor(config: Partial<CircuitBreakerConfig> = {}, logger: Logger) {
    this.config = {
      failureThreshold: config.failureThreshold || 5,
      resetTimeoutMs: config.resetTimeoutMs || 60000,
      monitoringPeriodMs: config.monitoringPeriodMs || 300000
    };
    this.logger = logger;
    this.logger.setContext({ component: 'CircuitBreaker' });
  }

  /**
   * Execute operation with circuit breaker protection
   */
  public async execute<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    // Check if circuit should be opened
    this.updateState();

    if (this.state === 'OPEN') {
      const error = new Error(`Circuit breaker is OPEN for operation: ${operationName}`) as IntegrationError;
      error.code = 'CIRCUIT_BREAKER_OPEN';
      error.service = 'integration';
      error.operation = operationName;
      error.retryable = true;
      throw error;
    }

    try {
      const result = await operation();
      this.onSuccess(operationName);
      return result;

    } catch (error) {
      this.onFailure(operationName, error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Handle successful operation
   */
  private onSuccess(operationName: string): void {
    this.successCount++;
    
    if (this.state === 'HALF_OPEN') {
      this.logger.info(`Circuit breaker transitioning to CLOSED: ${operationName}`);
      this.state = 'CLOSED';
      this.failureCount = 0;
    }
  }

  /**
   * Handle failed operation
   */
  private onFailure(operationName: string, error: Error): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    this.logger.warn(`Circuit breaker recorded failure: ${operationName}`, {
      failureCount: this.failureCount,
      threshold: this.config.failureThreshold,
      error: error.message
    });

    if (this.failureCount >= this.config.failureThreshold && this.state === 'CLOSED') {
      this.logger.error(`Circuit breaker opening: ${operationName}`, {
        failureCount: this.failureCount,
        threshold: this.config.failureThreshold
      });
      this.state = 'OPEN';
    }
  }

  /**
   * Update circuit breaker state based on time and conditions
   */
  private updateState(): void {
    const now = Date.now();

    if (this.state === 'OPEN' && now - this.lastFailureTime >= this.config.resetTimeoutMs) {
      this.logger.info('Circuit breaker transitioning to HALF_OPEN');
      this.state = 'HALF_OPEN';
      this.successCount = 0;
    }

    // Reset failure count after monitoring period
    if (now - this.lastFailureTime >= this.config.monitoringPeriodMs) {
      this.failureCount = 0;
    }
  }

  /**
   * Get current circuit breaker state
   */
  public getState(): {
    state: string;
    failureCount: number;
    successCount: number;
    lastFailureTime: number;
  } {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      lastFailureTime: this.lastFailureTime
    };
  }

  /**
   * Manually reset circuit breaker
   */
  public reset(): void {
    this.logger.info('Circuit breaker manually reset');
    this.state = 'CLOSED';
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = 0;
  }
}

/**
 * Error Recovery Strategies
 */
export class ErrorRecoveryManager {
  private readonly retryHandler: RetryHandler;
  private readonly circuitBreaker: CircuitBreaker;
  private readonly logger: Logger;

  constructor(
    retryConfig: Partial<RetryConfig> = {},
    circuitBreakerConfig: Partial<CircuitBreakerConfig> = {},
    logger: Logger
  ) {
    this.retryHandler = new RetryHandler(retryConfig, logger);
    this.circuitBreaker = new CircuitBreaker(circuitBreakerConfig, logger);
    this.logger = logger;
    this.logger.setContext({ component: 'ErrorRecoveryManager' });
  }

  /**
   * Execute operation with full error recovery (retry + circuit breaker)
   */
  public async executeWithRecovery<T>(
    operation: () => Promise<T>,
    operationName: string,
    retryConfig?: Partial<RetryConfig>
  ): Promise<RetryResult<T>> {
    return this.retryHandler.executeWithRetry(
      () => this.circuitBreaker.execute(operation, operationName),
      operationName,
      retryConfig
    );
  }

  /**
   * Get recovery status
   */
  public getStatus(): {
    circuitBreaker: ReturnType<CircuitBreaker['getState']>;
  } {
    return {
      circuitBreaker: this.circuitBreaker.getState()
    };
  }

  /**
   * Reset all recovery mechanisms
   */
  public reset(): void {
    this.circuitBreaker.reset();
    this.logger.info('Error recovery manager reset');
  }
}
