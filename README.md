# Encompass-GoHighLevel Integration System

A comprehensive TypeScript-based integration system that automatically syncs mortgage lead data from Encompass (ICE Mortgage Technology's API) to GoHighLevel CRM without manual intervention.

## Features

- **Automated Data Sync**: Periodically pulls lead data from Encompass and pushes to GoHighLevel
- **OAuth2 Authentication**: Secure authentication with Encompass API
- **Data Transformation**: Intelligent mapping between Encompass and GoHighLevel data formats
- **Error Handling**: Comprehensive retry logic with exponential backoff and circuit breaker patterns
- **Monitoring & Alerting**: Built-in monitoring with configurable alerts
- **Type Safety**: Full TypeScript implementation with strict type checking
- **Comprehensive Logging**: Structured logging with Winston
- **Rate Limiting**: Respects API rate limits for both services
- **Health Checks**: Real-time system health monitoring

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Encompass     │    │   Integration    │    │  GoHighLevel    │
│      API        │◄──►│     System       │◄──►│      CRM        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                       ┌──────▼──────┐
                       │  Monitoring │
                       │   & Alerts  │
                       └─────────────┘
```

## Quick Start

### Prerequisites

- Node.js 18+ and npm
- Encompass API credentials
- GoHighLevel API access

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd encompass-ghl-integration
```

2. Install dependencies:
```bash
npm install
```

3. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your API credentials
```

4. Build the project:
```bash
npm run build
```

5. Run tests:
```bash
npm test
```

6. Start the integration:
```bash
npm start
```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Encompass API Configuration
ENCOMPASS_API_URL=https://api.elliemae.com
ENCOMPASS_CLIENT_ID=your_client_id
ENCOMPASS_CLIENT_SECRET=your_client_secret
ENCOMPASS_USERNAME=your_username
ENCOMPASS_PASSWORD=your_password
ENCOMPASS_INSTANCE=your_instance_id

# GoHighLevel API Configuration
GHL_API_URL=https://rest.gohighlevel.com/v1
GHL_API_KEY=your_api_key
GHL_LOCATION_ID=your_location_id

# Sync Configuration
SYNC_INTERVAL_MINUTES=5
SYNC_BATCH_SIZE=100
SYNC_MAX_RETRIES=3
SYNC_RETRY_DELAY_MS=5000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/integration.log
LOG_MAX_FILES=10
LOG_MAX_SIZE=10m

# Environment
NODE_ENV=production
```

### Data Mapping

The system maps the following fields from Encompass to GoHighLevel:

| Encompass Field | GoHighLevel Field | Type |
|----------------|-------------------|------|
| Borrower Name | firstName, lastName | Standard |
| Email Address | email | Standard |
| Phone Numbers | phone, custom fields | Standard + Custom |
| Property Address | address1, city, state, postalCode | Standard |
| Date of Birth | date_of_birth | Custom Field |
| Loan Information | Various custom fields | Custom Field |
| Realtor Information | Various custom fields | Custom Field |
| Loan Originator | Various custom fields | Custom Field |

## API Documentation

### Health Check Endpoint

```typescript
// Get system health status
const healthCheck = await app.getHealthCheck();
console.log(healthCheck.status); // 'healthy', 'degraded', or 'unhealthy'
```

### Manual Sync

```typescript
// Trigger manual sync
const syncResult = await app.performManualSync();
console.log(`Processed ${syncResult.totalProcessed} records`);
```

### System Status

```typescript
// Get current system status
const status = app.getStatus();
console.log(status.isRunning); // true/false
console.log(status.metrics); // Performance metrics
```

## Development

### Project Structure

```
src/
├── config/           # Configuration management
├── services/         # Core business logic
│   ├── encompass-auth.ts
│   ├── encompass-borrower.ts
│   ├── gohighlevel.ts
│   ├── data-transformer.ts
│   ├── sync-orchestrator.ts
│   └── monitoring.ts
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
├── __tests__/       # Unit tests
└── index.ts         # Main entry point
```

### Available Scripts

```bash
# Development
npm run dev          # Start in development mode
npm run build        # Build for production
npm run start        # Start production build

# Testing
npm test             # Run all tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage

# Code Quality
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run type-check   # Run TypeScript compiler check
```

### Adding Custom Field Mappings

To add custom field mappings, modify the `DataTransformerService`:

```typescript
// In src/services/data-transformer.ts
private buildCustomFields(record: ComprehensiveBorrowerRecord): GHLCustomField[] {
  const customFields: GHLCustomField[] = [];
  
  // Add your custom field mapping
  addField('custom_field_name', record.borrower.customProperty);
  
  return customFields;
}
```

## Monitoring & Alerting

The system includes comprehensive monitoring with the following metrics:

- **Sync Metrics**: Success rate, error rate, throughput
- **API Metrics**: Response times, error rates for both APIs
- **System Metrics**: CPU, memory, disk usage
- **Error Tracking**: Categorized error logging

### Default Alerts

- High sync error rate (>25% warning, >50% critical)
- High memory usage (>85%)
- API error rates (>30%)

### Custom Alerts

Add custom alert rules:

```typescript
monitoringService.addAlertRule({
  name: 'Custom Alert',
  metric: 'sync.throughput',
  operator: 'less_than',
  threshold: 10,
  severity: 'warning',
  message: 'Sync throughput is below 10 records/minute'
});
```

## Deployment

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist/ ./dist/
EXPOSE 3000
CMD ["npm", "start"]
```

### PM2 Deployment

```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start dist/index.js --name "encompass-ghl-integration"

# Monitor
pm2 monit

# Logs
pm2 logs encompass-ghl-integration
```

### Environment-Specific Configurations

The system automatically applies environment-specific configurations:

- **Development**: Debug logging, smaller batch sizes
- **Test**: Error-only logging, fast intervals
- **Production**: Optimized settings, info logging

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify API credentials in `.env`
   - Check token expiration and refresh logic

2. **Rate Limiting**
   - Adjust `SYNC_INTERVAL_MINUTES` to reduce frequency
   - Monitor API usage in logs

3. **Data Transformation Errors**
   - Check field mappings in transformer service
   - Validate input data format

4. **Memory Issues**
   - Reduce `SYNC_BATCH_SIZE`
   - Monitor memory usage metrics

### Logs

Logs are structured JSON format with the following levels:
- `error`: Critical errors requiring attention
- `warn`: Warnings that may need investigation
- `info`: General operational information
- `debug`: Detailed debugging information

Example log entry:
```json
{
  "level": "info",
  "message": "Sync operation completed",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "context": {
    "batchId": "batch_1705315800000_abc123",
    "totalProcessed": 150,
    "successful": 148,
    "failed": 2,
    "duration": 45000
  }
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the troubleshooting section
- Review the logs for error details
- Create an issue in the repository
- Contact the development team

## Changelog

### v1.0.0
- Initial release
- Complete Encompass-GoHighLevel integration
- Automated sync with monitoring
- Comprehensive error handling
- Full TypeScript implementation
