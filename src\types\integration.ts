/**
 * Integration System Type Definitions
 * Internal types for the Encompass-MongoDB integration
 */

// import { EncompassBorrowerContact, EncompassLoanData, EncompassLoanAssociate, EncompassBusinessContact } from './encompass';

// Configuration Types
export interface IntegrationConfig {
  encompass: {
    baseUrl: string;
    clientId: string;
    clientSecret: string;
    username: string;
    password: string;
    instance?: string;
  };
  gohighlevel?: {
    apiUrl: string;
    apiKey: string;
    locationId: string;
    pipelineId?: string;
    pipelineStageId?: string;
  };
  sync: {
    intervalMinutes: number;
    batchSize: number;
    maxRetries: number;
    retryDelayMs: number;
  };
  logging: {
    level: string;
    filePath: string;
    maxFiles: number;
    maxSize: string;
  };
}

// Comprehensive Data Record
export interface ComprehensiveBorrowerRecord {
  borrower: {
    id: string;
    name: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    phones: {
      home?: string;
      work?: string;
      mobile?: string;
    };
    dateOfBirth?: string;
    address?: {
      street1?: string;
      street2?: string;
      city?: string;
      state?: string;
      zip?: string;
    };
    employer?: {
      name?: string;
      jobTitle?: string;
    };
    referralText?: string; // Contains realtor/originator information
  };
  loan?: {
    id?: string;
    guid?: string;
    interestRate?: number;
    closingDate?: string;
    propertyAddress?: string;
    loanOriginator?: {
      name?: string;
      email?: string;
      phone?: string;
      role?: string;
      id?: string;
    };
    amount?: number;
    type?: string;
  };
  realtor?: {
    name?: string;
    phone?: string;
    email?: string;
    businessContactId?: string;
  };
  dataSources: {
    borrowerContact: boolean;
    loanData: boolean;
    loanAssociates: boolean;
    realtorBusinessContact: boolean;
  };
  metadata: {
    extractedAt: string;
    source: string;
    version: string;
  };
}

// Data Transformation Types
export interface DataTransformationResult {
  contact: any; // Generic contact object
  customFields: any[];
  tags: string[];
  source: string;
  transformedAt: string;
}

export interface TransformationError {
  field: string;
  value: unknown;
  error: string;
  severity: 'warning' | 'error';
}

export interface TransformationResult {
  success: boolean;
  data?: DataTransformationResult;
  errors: TransformationError[];
  warnings: TransformationError[];
}

// Sync Operation Types
export interface SyncOperation {
  id: string;
  type: 'create' | 'update' | 'skip';
  encompassContactId: string;
  contactId?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
  startedAt?: string;
  completedAt?: string;
  error?: string;
  retryCount: number;
  data: ComprehensiveBorrowerRecord;
}

export interface SyncBatch {
  id: string;
  operations: SyncOperation[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startedAt: string;
  completedAt?: string;
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  skippedOperations: number;
}

export interface SyncResult {
  batchId: string;
  success: boolean;
  totalProcessed: number;
  successful: number;
  failed: number;
  skipped: number;
  errors: Array<{
    operationId: string;
    encompassContactId: string;
    error: string;
  }>;
  duration: number;
  completedAt: string;
}

// Monitoring and Metrics Types
export interface SyncMetrics {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  averageDuration: number;
  lastSyncAt?: string;
  lastSuccessfulSyncAt?: string;
  lastFailedSyncAt?: string;
  errorRate: number;
  throughput: number; // records per minute
}

export interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: {
    encompass: {
      status: 'up' | 'down';
      responseTime?: number;
      lastChecked: string;
      error?: string;
    };
    mongodb: {
      status: 'up' | 'down';
      responseTime?: number;
      lastChecked: string;
      error?: string;
    };
  };
  metrics: SyncMetrics;
}

// Error Types
export interface IntegrationError extends Error {
  code: string;
  service: 'encompass' | 'mongodb' | 'integration';
  operation: string;
  retryable: boolean;
  context?: Record<string, unknown>;
}

// Event Types
export interface IntegrationEvent {
  id: string;
  type: 'sync_started' | 'sync_completed' | 'sync_failed' | 'contact_created' | 'contact_updated' | 'error_occurred';
  timestamp: string;
  data: Record<string, unknown>;
  severity: 'info' | 'warning' | 'error';
}

// API Client Types
export interface ApiClientConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  headers?: Record<string, string>;
}

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
  headers?: Record<string, string>;
}

// Field Mapping Types
export interface FieldMapping {
  encompassField: string;
  targetField: string;
  transform?: (value: unknown) => unknown;
  required?: boolean;
  defaultValue?: unknown;
}

export interface MappingRule {
  name: string;
  description: string;
  mappings: FieldMapping[];
  conditions?: Array<{
    field: string;
    operator: 'equals' | 'contains' | 'exists' | 'not_exists';
    value?: unknown;
  }>;
}

// Validation Types
export interface ValidationRule {
  field: string;
  type: 'required' | 'email' | 'phone' | 'date' | 'number' | 'string' | 'custom';
  message: string;
  validator?: (value: unknown) => boolean;
}

export interface ValidationResult {
  valid: boolean;
  errors: Array<{
    field: string;
    message: string;
    value: unknown;
  }>;
}
