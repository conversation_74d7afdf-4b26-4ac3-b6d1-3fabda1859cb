/**
 * Integration Configuration
 * Centralized configuration for the complete integration system
 */

import { IntegrationManagerConfig } from '../services/integration-manager';

// Default configuration
export const defaultIntegrationConfig: IntegrationManagerConfig = {
  encompass: {
    baseUrl: process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com',
    username: process.env.ENCOMPASS_USERNAME || 'contractoradmin@encompass:BE11140034',
    password: process.env.ENCOMPASS_PASSWORD || 'ReadyGHL@0515',
    clientId: process.env.ENCOMPASS_CLIENT_ID || 'hmrxqy5',
    clientSecret: process.env.ENCOMPASS_CLIENT_SECRET || 'V6@9u4xoqAPv9NV2U9MrL^CnJe9HMBwL*^70u0#P8!m9JFiDndVryp!932cNAvLw',
    instance: process.env.ENCOMPASS_INSTANCE || 'BE11140034',
    batchSize: parseInt(process.env.ENCOMPASS_BATCH_SIZE || '500'),
    enableMongoCache: process.env.ENABLE_MONGO_CACHE === 'true'
  },

  mongodb: {
    connectionString: process.env.MONGODB_CONNECTION_STRING || 'mongodb://localhost:27017',
    databaseName: process.env.MONGODB_DATABASE_NAME || 'encompass_integration',
    contactsCollectionName: process.env.MONGODB_CONTACTS_COLLECTION || 'borrower_contacts',
    syncLogsCollectionName: process.env.MONGODB_SYNC_LOGS_COLLECTION || 'sync_logs',
    maxRetries: parseInt(process.env.MONGODB_MAX_RETRIES || '3'),
    retryDelayMs: parseInt(process.env.MONGODB_RETRY_DELAY_MS || '1000'),
    connectionTimeout: parseInt(process.env.MONGODB_CONNECTION_TIMEOUT || '30000'),
    queryTimeout: parseInt(process.env.MONGODB_QUERY_TIMEOUT || '30000'),
    enableChangeDetection: process.env.ENABLE_CHANGE_DETECTION !== 'false',
    enableAutoSync: process.env.ENABLE_AUTO_SYNC !== 'false',
    syncIntervalMinutes: parseInt(process.env.SYNC_INTERVAL_MINUTES || '60'),
    batchSize: parseInt(process.env.MONGO_BATCH_SIZE || '100'),
    maxConcurrentSyncs: parseInt(process.env.MAX_CONCURRENT_SYNCS || '5'),
    enableMetrics: process.env.ENABLE_METRICS !== 'false',
    metricsRetentionDays: parseInt(process.env.METRICS_RETENTION_DAYS || '30')
  },

  sync: {
    cronExpression: process.env.SYNC_CRON_EXPRESSION || '0 * * * *', // Every hour
    batchSize: parseInt(process.env.SYNC_BATCH_SIZE || '100'),
    maxConcurrentSyncs: parseInt(process.env.MAX_CONCURRENT_SYNCS || '5'),
    enableAutoSync: process.env.ENABLE_AUTO_SYNC !== 'false',
    syncTimeoutMs: parseInt(process.env.SYNC_TIMEOUT_MS || '300000'), // 5 minutes
    retryDelayMs: parseInt(process.env.SYNC_RETRY_DELAY_MS || '5000'),
    maxRetries: parseInt(process.env.SYNC_MAX_RETRIES || '3')
  },

  gohighlevel: {
    apiUrl: process.env.GHL_API_URL || 'https://rest.gohighlevel.com/v1',
    apiKey: process.env.GHL_API_KEY || '',
    locationId: process.env.GHL_LOCATION_ID || '',
    pipelineId: process.env.GHL_PIPELINE_ID || '',
    pipelineStageId: process.env.GHL_PIPELINE_STAGE_ID || ''
  }
};

// Environment-specific configurations
export const developmentConfig: Partial<IntegrationManagerConfig> = {
  mongodb: {
    ...defaultIntegrationConfig.mongodb,
    databaseName: 'encompass_integration_dev',
    enableMetrics: true
  },
  sync: {
    ...defaultIntegrationConfig.sync,
    cronExpression: '*/15 * * * *', // Every 15 minutes for development
    enableAutoSync: false // Disable auto sync in development
  }
};

export const productionConfig: Partial<IntegrationManagerConfig> = {
  mongodb: {
    ...defaultIntegrationConfig.mongodb,
    databaseName: 'encompass_integration_prod',
    maxRetries: 5,
    connectionTimeout: 60000,
    queryTimeout: 60000
  },
  sync: {
    ...defaultIntegrationConfig.sync,
    cronExpression: '0 * * * *', // Every hour
    enableAutoSync: true,
    maxRetries: 5,
    syncTimeoutMs: 600000 // 10 minutes
  }
};

export const testConfig: Partial<IntegrationManagerConfig> = {
  mongodb: {
    ...defaultIntegrationConfig.mongodb,
    databaseName: 'encompass_integration_test',
    enableAutoSync: false,
    enableMetrics: false
  },
  sync: {
    ...defaultIntegrationConfig.sync,
    enableAutoSync: false,
    batchSize: 10
  }
};

/**
 * Get configuration based on environment
 */
export function getIntegrationConfig(): IntegrationManagerConfig {
  const env = process.env.NODE_ENV || 'development';
  
  let envConfig: Partial<IntegrationManagerConfig> = {};
  
  switch (env) {
    case 'production':
      envConfig = productionConfig;
      break;
    case 'test':
      envConfig = testConfig;
      break;
    case 'development':
    default:
      envConfig = developmentConfig;
      break;
  }

  // Deep merge configurations
  const config: IntegrationManagerConfig = {
    encompass: { ...defaultIntegrationConfig.encompass, ...envConfig.encompass },
    mongodb: { ...defaultIntegrationConfig.mongodb, ...envConfig.mongodb },
    sync: { ...defaultIntegrationConfig.sync, ...envConfig.sync }
  };

  // Only add gohighlevel if it's configured
  if (defaultIntegrationConfig.gohighlevel || envConfig.gohighlevel) {
    config.gohighlevel = {
      apiUrl: defaultIntegrationConfig.gohighlevel?.apiUrl || envConfig.gohighlevel?.apiUrl || '',
      apiKey: defaultIntegrationConfig.gohighlevel?.apiKey || envConfig.gohighlevel?.apiKey || '',
      locationId: defaultIntegrationConfig.gohighlevel?.locationId || envConfig.gohighlevel?.locationId || '',
      pipelineId: defaultIntegrationConfig.gohighlevel?.pipelineId || envConfig.gohighlevel?.pipelineId || '',
      pipelineStageId: defaultIntegrationConfig.gohighlevel?.pipelineStageId || envConfig.gohighlevel?.pipelineStageId || ''
    };
  }

  return config;
}

/**
 * Validate configuration
 */
export function validateIntegrationConfig(config: IntegrationManagerConfig): string[] {
  const errors: string[] = [];

  // Validate Encompass configuration
  if (!config.encompass.baseUrl) errors.push('Encompass baseUrl is required');
  if (!config.encompass.username) errors.push('Encompass username is required');
  if (!config.encompass.password) errors.push('Encompass password is required');
  if (!config.encompass.clientId) errors.push('Encompass clientId is required');
  if (!config.encompass.clientSecret) errors.push('Encompass clientSecret is required');
  if (!config.encompass.instance) errors.push('Encompass instance is required');

  // Validate MongoDB configuration
  if (!config.mongodb.connectionString) errors.push('MongoDB connectionString is required');
  if (!config.mongodb.databaseName) errors.push('MongoDB databaseName is required');
  if (!config.mongodb.contactsCollectionName) errors.push('MongoDB contactsCollectionName is required');
  if (!config.mongodb.syncLogsCollectionName) errors.push('MongoDB syncLogsCollectionName is required');

  // Validate sync configuration
  if (!config.sync.cronExpression) errors.push('Sync cronExpression is required');
  if (config.sync.batchSize <= 0) errors.push('Sync batchSize must be greater than 0');
  if (config.sync.maxConcurrentSyncs <= 0) errors.push('Sync maxConcurrentSyncs must be greater than 0');

  // Validate GoHighLevel configuration (if provided)
  if (config.gohighlevel?.apiKey && !config.gohighlevel.apiUrl) {
    errors.push('GoHighLevel apiUrl is required when apiKey is provided');
  }
  if (config.gohighlevel?.apiKey && !config.gohighlevel.locationId) {
    errors.push('GoHighLevel locationId is required when apiKey is provided');
  }

  return errors;
}

/**
 * Environment variables documentation
 */
export const environmentVariables = {
  // Encompass Configuration
  ENCOMPASS_API_URL: 'Encompass API base URL (default: https://api.elliemae.com)',
  ENCOMPASS_USERNAME: 'Encompass username with instance (e.g., user@encompass:INSTANCE)',
  ENCOMPASS_PASSWORD: 'Encompass password',
  ENCOMPASS_CLIENT_ID: 'Encompass OAuth client ID',
  ENCOMPASS_CLIENT_SECRET: 'Encompass OAuth client secret',
  ENCOMPASS_INSTANCE: 'Encompass instance ID',
  ENCOMPASS_BATCH_SIZE: 'Batch size for Encompass API calls (default: 500)',
  ENABLE_MONGO_CACHE: 'Enable MongoDB caching (default: false)',

  // MongoDB Configuration
  MONGODB_CONNECTION_STRING: 'MongoDB connection string (default: mongodb://localhost:27017)',
  MONGODB_DATABASE_NAME: 'MongoDB database name (default: encompass_integration)',
  MONGODB_CONTACTS_COLLECTION: 'MongoDB contacts collection name (default: borrower_contacts)',
  MONGODB_SYNC_LOGS_COLLECTION: 'MongoDB sync logs collection name (default: sync_logs)',
  MONGODB_MAX_RETRIES: 'MongoDB max retry attempts (default: 3)',
  MONGODB_RETRY_DELAY_MS: 'MongoDB retry delay in milliseconds (default: 1000)',
  MONGODB_CONNECTION_TIMEOUT: 'MongoDB connection timeout in milliseconds (default: 30000)',
  MONGODB_QUERY_TIMEOUT: 'MongoDB query timeout in milliseconds (default: 30000)',

  // Sync Configuration
  SYNC_CRON_EXPRESSION: 'Cron expression for sync schedule (default: 0 * * * * - every hour)',
  SYNC_BATCH_SIZE: 'Batch size for sync operations (default: 100)',
  MAX_CONCURRENT_SYNCS: 'Maximum concurrent sync operations (default: 5)',
  ENABLE_AUTO_SYNC: 'Enable automatic sync scheduling (default: true)',
  SYNC_TIMEOUT_MS: 'Sync operation timeout in milliseconds (default: 300000)',
  SYNC_RETRY_DELAY_MS: 'Sync retry delay in milliseconds (default: 5000)',
  SYNC_MAX_RETRIES: 'Maximum sync retry attempts (default: 3)',

  // Feature Flags
  ENABLE_CHANGE_DETECTION: 'Enable change detection (default: true)',
  ENABLE_METRICS: 'Enable metrics collection (default: true)',
  METRICS_RETENTION_DAYS: 'Metrics retention period in days (default: 30)',

  // GoHighLevel Configuration (Optional)
  GHL_API_URL: 'GoHighLevel API URL (default: https://rest.gohighlevel.com/v1)',
  GHL_API_KEY: 'GoHighLevel API key',
  GHL_LOCATION_ID: 'GoHighLevel location ID',

  // General
  NODE_ENV: 'Environment (development, production, test)'
};

export default {
  defaultIntegrationConfig,
  developmentConfig,
  productionConfig,
  testConfig,
  getIntegrationConfig,
  validateIntegrationConfig,
  environmentVariables
};
