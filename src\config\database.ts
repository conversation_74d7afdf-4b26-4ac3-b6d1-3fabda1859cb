/**
 * Database Connection Configuration
 * MongoDB connection using Mongoose with automatic reconnection
 */

import mongoose from 'mongoose';
import { Logger } from '../utils/simple-logger';

const logger = new Logger('Database');

export interface DatabaseConfig {
  connectionString: string;
  databaseName: string;
  retryDelayMs: number;
  connectionTimeout: number;
  queryTimeout: number;
  maxRetries: number;
}

export async function connectDatabase(config?: DatabaseConfig): Promise<void> {
  try {
    const mongoUri = config?.connectionString || process.env.MONGODB_CONNECTION_STRING || 'mongodb://localhost:27017';
    const dbName = config?.databaseName || process.env.MONGODB_DATABASE_NAME || 'encompass_integration';
    const useMockMode = process.env.USE_MOCK_DATABASE === 'true';

    if (useMockMode) {
      logger.info('🎭 Using mock database mode (MongoDB not required)');
      logger.info('� Set USE_MOCK_DATABASE=false in .env to use real MongoDB');
      return;
    }

    logger.info('�🔌 Connecting to MongoDB...', {
      uri: mongoUri.replace(/\/\/.*@/, '//***:***@'), // Hide credentials in logs
      database: dbName
    });

    await mongoose.connect(mongoUri, {
      dbName: dbName,
      serverSelectionTimeoutMS: config?.connectionTimeout || parseInt(process.env.MONGODB_CONNECTION_TIMEOUT || '30000'),
      socketTimeoutMS: config?.queryTimeout || parseInt(process.env.MONGODB_QUERY_TIMEOUT || '30000'),
      maxPoolSize: 10,
      minPoolSize: 2,
      maxIdleTimeMS: 30000,
      bufferCommands: false,
      retryWrites: true,
      retryReads: true,
    });

    logger.info('✅ Connected to MongoDB successfully');

    // Set up connection event handlers
    setupConnectionHandlers();

  } catch (error) {
    logger.error('💥 Failed to connect to MongoDB:', { error });

    // Provide helpful suggestions
    logger.info('💡 Quick solutions:');
    logger.info('   1. Install MongoDB: https://www.mongodb.com/try/download/community');
    logger.info('   2. Start service: net start MongoDB');
    logger.info('   3. Use Docker: docker run -d -p 27017:27017 mongo:latest');
    logger.info('   4. Use mock mode: Set USE_MOCK_DATABASE=true in .env');

    throw error;
  }
}

export async function disconnectDatabase(): Promise<void> {
  try {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from MongoDB');
  } catch (error) {
    logger.error('💥 Error disconnecting from MongoDB:', { error });
    throw error;
  }
}

export function isConnected(): boolean {
  return mongoose.connection.readyState === 1;
}

export function getConnectionState(): string {
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting'
  };
  return states[mongoose.connection.readyState as keyof typeof states] || 'unknown';
}

export function getDatabaseStats(): any {
  return {
    readyState: mongoose.connection.readyState,
    state: getConnectionState(),
    host: mongoose.connection.host,
    port: mongoose.connection.port,
    name: mongoose.connection.name,
    collections: Object.keys(mongoose.connection.collections)
  };
}

function setupConnectionHandlers(): void {
  mongoose.connection.on('error', (error) => {
    logger.error('❌ MongoDB connection error:', { error });
  });

  mongoose.connection.on('disconnected', () => {
    logger.warn('⚠️ MongoDB disconnected');
  });

  mongoose.connection.on('reconnected', () => {
    logger.info('🔄 MongoDB reconnected');
  });

  mongoose.connection.on('connecting', () => {
    logger.info('🔄 MongoDB connecting...');
  });

  mongoose.connection.on('connected', () => {
    logger.info('✅ MongoDB connected');
  });

  mongoose.connection.on('disconnecting', () => {
    logger.info('🔌 MongoDB disconnecting...');
  });

  // Handle process termination
  process.on('SIGINT', async () => {
    logger.info('🛑 Received SIGINT, closing MongoDB connection...');
    await mongoose.connection.close();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    logger.info('🛑 Received SIGTERM, closing MongoDB connection...');
    await mongoose.connection.close();
    process.exit(0);
  });
}

// Export mongoose for direct access if needed
export { mongoose };
export default mongoose;
