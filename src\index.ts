/**
 * Main Entry Point
 * Encompass-MongoDB Integration System
 */

import { getIntegrationConfig, validateIntegrationConfig } from './config/integration';
import { connectDatabase, disconnectDatabase, isConnected, getDatabaseStats } from './config/database';
import { Logger } from './utils/simple-logger';
import { IntegrationManagerService } from './services/integration-manager';

/**
 * Integration Application Class
 */
class IntegrationApp {
  private integrationManager: IntegrationManagerService | null = null;
  private logger = new Logger('IntegrationApp');
  private isRunning = false;

  /**
   * Initialize the application
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('🚀 Initializing Encompass-MongoDB Integration System');

      // Load and validate configuration
      this.logger.info('📋 Loading configuration...');
      const config = getIntegrationConfig();

      const configErrors = validateIntegrationConfig(config);
      if (configErrors.length > 0) {
        this.logger.error('❌ Configuration validation failed:', { errors: configErrors });
        throw new Error(`Configuration validation failed: ${configErrors.join(', ')}`);
      }

      this.logger.info('✅ Configuration loaded and validated');

      // Connect to MongoDB using Mongoose
      this.logger.info('🔌 Connecting to MongoDB...');
      await connectDatabase({
        connectionString: config.mongodb.connectionString,
        databaseName: config.mongodb.databaseName,
        retryDelayMs: config.mongodb.retryDelayMs,
        connectionTimeout: config.mongodb.connectionTimeout,
        queryTimeout: config.mongodb.queryTimeout,
        maxRetries: config.mongodb.maxRetries
      });

      // Verify database connection
      if (!isConnected()) {
        throw new Error('Failed to establish MongoDB connection');
      }

      const dbStats = getDatabaseStats();
      this.logger.info('✅ MongoDB connected successfully', { database: dbStats });

      // Initialize Integration Manager (handles all MongoDB operations)
      this.logger.info('🔧 Initializing Integration Manager...');
      this.integrationManager = new IntegrationManagerService(config, this.logger);
      await this.integrationManager.initialize();

      this.logger.info('✅ Integration system initialized successfully');

    } catch (error) {
      this.logger.error('💥 Failed to initialize integration system', { error });
      throw error;
    }
  }

  /**
   * Start the integration system
   */
  public async start(): Promise<void> {
    if (!this.integrationManager) {
      throw new Error('Integration system not initialized. Call initialize() first.');
    }

    try {
      this.logger.info('🚀 Starting integration system');

      // Perform health check before starting
      const healthCheck = await this.integrationManager.healthCheck();
      this.logger.info('🏥 Health check completed', { healthCheck });

      if (healthCheck.status === 'unhealthy') {
        throw new Error('Health check failed. Cannot start integration system.');
      }

      // The integration manager handles all sync operations
      this.isRunning = true;

      this.logger.info('✅ Integration system started successfully');
      this.logger.info('💡 System is ready for manual sync operations and health checks');

    } catch (error) {
      this.logger.error('💥 Failed to start integration system', { error });
      throw error;
    }
  }

  /**
   * Stop the integration system
   */
  public async stop(): Promise<void> {
    if (!this.integrationManager) {
      this.logger.warn('Integration system not initialized');
      return;
    }

    try {
      this.logger.info('🛑 Stopping integration system');

      if (this.integrationManager) {
        await this.integrationManager.shutdown();
      }

      // Disconnect from MongoDB
      await disconnectDatabase();

      this.isRunning = false;
      this.logger.info('✅ Integration system stopped successfully');

    } catch (error) {
      this.logger.error('💥 Error stopping integration system', { error });
      throw error;
    }
  }

  /**
   * Get system status
   */
  public getStatus(): any {
    if (!this.integrationManager) {
      return { status: 'not_initialized' };
    }

    return {
      status: this.isRunning ? 'running' : 'stopped',
      isRunning: this.isRunning,
      hasIntegrationManager: !!this.integrationManager
    };
  }

  /**
   * Get health check
   */
  public async getHealthCheck(): Promise<any> {
    if (!this.integrationManager) {
      return { status: 'not_initialized' };
    }

    return await this.integrationManager.healthCheck();
  }

  /**
   * Perform manual sync
   */
  public async performManualSync(): Promise<any> {
    if (!this.integrationManager) {
      throw new Error('Integration system not initialized');
    }

    this.logger.info('⚡ Performing manual sync');
    return await this.integrationManager.runManualSync();
  }

  /**
   * Fetch and cache contacts
   */
  public async fetchAndCacheContacts(limit: number = 500, start: number = 0): Promise<any[]> {
    if (!this.integrationManager) {
      throw new Error('Integration system not initialized');
    }

    this.logger.info(`📥 Fetching and caching ${limit} contacts starting at ${start}`);
    return await this.integrationManager.fetchAndCacheContacts(limit, start);
  }

  /**
   * Get dashboard data
   */
  public async getDashboardData(): Promise<any> {
    if (!this.integrationManager) {
      throw new Error('Integration system not initialized');
    }

    return await this.integrationManager.getDashboardData();
  }

  /**
   * Get database connection status
   */
  public getDatabaseStatus(): any {
    return {
      connected: isConnected(),
      state: getDatabaseStats()
    };
  }


}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  const app = new IntegrationApp();

  // Handle graceful shutdown
  const shutdown = async (signal: string): Promise<void> => {
    console.log(`\nReceived ${signal}. Shutting down gracefully...`);
    
    try {
      await app.stop();
      console.log('Integration system stopped successfully');
      process.exit(0);
    } catch (error) {
      console.error('Error during shutdown:', error);
      process.exit(1);
    }
  };

  // Setup signal handlers
  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    shutdown('uncaughtException').catch(() => process.exit(1));
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    shutdown('unhandledRejection').catch(() => process.exit(1));
  });

  try {
    // Initialize and start the application
    await app.initialize();
    await app.start();

    console.log('🎉 MongoDB Integration system is running. Press Ctrl+C to stop.');
    console.log('💡 Available operations:');
    console.log('  - Automated sync every hour');
    console.log('  - Manual sync via app.performManualSync()');
    console.log('  - Health checks via app.getHealthCheck()');
    console.log('  - Dashboard data via app.getDashboardData()');

    // Perform an initial test
    console.log('\n🧪 Running initial test...');
    const health = await app.getHealthCheck();
    const dbStatus = app.getDatabaseStatus();
    console.log(`🏥 System health: ${health.status}`);
    console.log(`🔌 Database status: ${dbStatus.connected ? 'Connected' : 'Disconnected'} (${dbStatus.state.state})`);

    // Keep the process alive and perform periodic health checks
    setInterval(async () => {
      try {
        const status = app.getStatus();
        if (status.isRunning) {
          const health = await app.getHealthCheck();
          const dbStatus = app.getDatabaseStatus();
          console.log(`📊 Health check: ${health.status} | DB: ${dbStatus.connected ? 'Connected' : 'Disconnected'} at ${new Date().toISOString()}`);
        }
      } catch (error) {
        console.error('Health check failed:', error);
      }
    }, 300000); // Every 5 minutes

  } catch (error) {
    console.error('💥 Failed to start integration system:', error);
    process.exit(1);
  }
}

// Export the app class for testing
export { IntegrationApp };

// Run the application if this file is executed directly
if (require.main === module) {
  main().catch((error) => {
    console.error('Application failed to start:', error);
    process.exit(1);
  });
}
