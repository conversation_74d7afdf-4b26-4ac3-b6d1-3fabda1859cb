/**
 * Encompass API Type Definitions
 * Based on Encompass Developer Connect API documentation
 */

// Authentication Types
export interface EncompassAuthConfig {
  baseUrl: string;
  clientId: string;
  clientSecret: string;
  username: string;
  password: string;
  instance?: string;
}

export interface EncompassTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
  scope?: string;
}

export interface EncompassAuthError {
  error: string;
  error_description: string;
}

// Address Types
export interface EncompassAddress {
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip?: string;
  county?: string;
  country?: string;
}

// Contact Types
export interface EncompassBorrowerContact {
  id: string;
  firstName?: string;
  lastName?: string;
  middleName?: string;
  suffix?: string;
  personalEmail?: string;
  businessEmail?: string;
  homePhone?: string;
  workPhone?: string;
  mobilePhone?: string;
  fax?: string;
  birthdate?: string;
  ssn?: string;
  currentMailingAddress?: EncompassAddress;
  employerName?: string;
  jobTitle?: string;
  referral?: string;
  maritalStatus?: string;
  dependentCount?: number;
  citizenship?: string;
  residencyType?: string;
  yearsInSchool?: number;
  created?: string;
  modified?: string;
}

// Batch Contact Structure (from borrowerContacts endpoint)
export interface EncompassBatchContact {
  borrowerType: number;
  firstName: string;
  lastName: string;
  fullName: string;
  dateOfBirth?: string; // Available in individual contact fetch
  personalEmail: string;
  businessEmail: string;
  homePhone: string;
  mobilePhone: string;
  workPhone: string;
  employerName: string;
  jobTitle: string;
  propertyAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    fullAddress: string;
  };
  loanId: string;
  loanAmount: string; // Usually empty, need individual fetch
  interestRate: string; // Usually empty, need individual fetch
  closingDate: string; // Usually empty, need individual fetch
  realtorName: string;
  realtorPhone: string;
  realtorEmail: string;
  loanOriginatorName: string;
  loanOriginatorPhone: string;
  loanOriginatorEmail: string;
  contactId: string;
  referralText: string;
  lastModified: string;
}

export interface EncompassBorrowerContactsResponse {
  borrowerContacts: EncompassBorrowerContact[];
  totalRecords: number;
  start: number;
  limit: number;
}

// Loan Types
export interface EncompassLoanData {
  loanNumber?: string;
  guid?: string;
  loanFolder?: string;
  loanProgram?: string;
  loanPurpose?: string;
  loanType?: string;
  loanAmount?: number;
  requestedLoanAmount?: number;
  interestRate?: number;
  noteRate?: number;
  closingDate?: string;
  estimatedClosingDate?: string;
  lockExpirationDate?: string;
  propertyAddress?: EncompassAddress;
  subjectPropertyAddress?: EncompassAddress;
  propertyType?: string;
  propertyUsage?: string;
  occupancyType?: string;
  numberOfUnits?: number;
  yearBuilt?: number;
  propertyValue?: number;
  downPayment?: number;
  loanToValue?: number;
  debtToIncome?: number;
  creditScore?: number;
  fields?: Record<string, unknown>;
  created?: string;
  modified?: string;
}

// Loan Associate Types
export interface EncompassLoanAssociate {
  id: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  role?: string;
  roleType?: string;
  licenseNumber?: string;
  nmlsId?: string;
  company?: string;
  isActive?: boolean;
}

// Business Contact Types (for Realtors)
export interface EncompassBusinessContact {
  id: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  email?: string;
  phone?: string;
  fax?: string;
  address?: EncompassAddress;
  contactType?: string;
  licenseNumber?: string;
  nmlsId?: string;
  website?: string;
  notes?: string;
  isActive?: boolean;
  created?: string;
  modified?: string;
}

// API Response Types
export interface EncompassApiResponse<T = unknown> {
  data?: T;
  totalRecords?: number;
  start?: number;
  limit?: number;
  hasMore?: boolean;
}

export interface EncompassErrorResponse {
  error?: {
    code?: string;
    message?: string;
    details?: string;
  };
  message?: string;
  statusCode?: number;
}

// Field Mapping Types
export interface EncompassFieldMapping {
  fieldId: string;
  fieldName: string;
  dataType: 'string' | 'number' | 'date' | 'boolean';
  description?: string;
  category?: string;
}

// Common Encompass Field IDs (based on standard field mappings)
export const ENCOMPASS_FIELD_IDS = {
  // Borrower Information
  BORROWER_FIRST_NAME: '4000',
  BORROWER_LAST_NAME: '4002',
  BORROWER_MIDDLE_NAME: '4001',
  BORROWER_EMAIL: '4008',
  BORROWER_HOME_PHONE: '4009',
  BORROWER_WORK_PHONE: '4010',
  BORROWER_MOBILE_PHONE: '4011',
  BORROWER_DOB: '4004',
  BORROWER_SSN: '4003',
  
  // Property Information
  PROPERTY_ADDRESS: '11',
  PROPERTY_CITY: '12',
  PROPERTY_STATE: '14',
  PROPERTY_ZIP: '15',
  PROPERTY_VALUE: '1821',
  
  // Loan Information
  LOAN_AMOUNT: '1109',
  INTEREST_RATE: '4',
  CLOSING_DATE: '763',
  LOAN_PURPOSE: '19',
  LOAN_TYPE: '1172',
  
  // Realtor Information
  REALTOR_NAME: '1401',
  REALTOR_PHONE: '1402',
  REALTOR_EMAIL: '1403',
  
  // Loan Officer Information
  LOAN_OFFICER_NAME: '317',
  LOAN_OFFICER_PHONE: '318',
  LOAN_OFFICER_EMAIL: '319',
} as const;

export type EncompassFieldId = typeof ENCOMPASS_FIELD_IDS[keyof typeof ENCOMPASS_FIELD_IDS];
