/**
 * Simple Logger Implementation
 * Basic logging functionality for the MongoDB integration system
 */

export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

export class Logger {
  private context: Record<string, unknown>;
  private level: LogLevel;

  constructor(
    levelOrContext: LogLevel | string = 'info',
    context: Record<string, unknown> = {}
  ) {
    // If first parameter is a string and not a valid log level, treat it as context
    if (typeof levelOrContext === 'string' && !['error', 'warn', 'info', 'debug'].includes(levelOrContext)) {
      this.context = { service: levelOrContext, ...context };
      this.level = 'info';
    } else {
      this.context = context;
      this.level = levelOrContext as LogLevel;
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels = ['error', 'warn', 'info', 'debug'];
    return levels.indexOf(level) <= levels.indexOf(this.level);
  }

  private formatMessage(level: LogLevel, message: string, context?: Record<string, unknown>): string {
    const timestamp = new Date().toISOString();
    const contextStr = context ? ` [${JSON.stringify({ ...this.context, ...context })}]` : '';
    return `${timestamp} [${level.toUpperCase()}]: ${message}${contextStr}`;
  }

  public error(message: string, context?: Record<string, unknown>): void {
    if (this.shouldLog('error')) {
      console.error(this.formatMessage('error', message, context));
    }
  }

  public warn(message: string, context?: Record<string, unknown>): void {
    if (this.shouldLog('warn')) {
      console.warn(this.formatMessage('warn', message, context));
    }
  }

  public info(message: string, context?: Record<string, unknown>): void {
    if (this.shouldLog('info')) {
      console.info(this.formatMessage('info', message, context));
    }
  }

  public debug(message: string, context?: Record<string, unknown>): void {
    if (this.shouldLog('debug')) {
      console.debug(this.formatMessage('debug', message, context));
    }
  }

  public setContext(context: Record<string, unknown>): void {
    this.context = { ...this.context, ...context };
  }

  public getContext(): Record<string, unknown> {
    return { ...this.context };
  }
}
